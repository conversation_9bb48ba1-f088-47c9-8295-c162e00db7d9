# Campaign Notification Rules System - <PERSON><PERSON> hoạch Triển khai

## 📋 Tổng quan Dự án

### Mụ<PERSON> tiêu

Xây dựng hệ thống rule thông báo động và linh hoạt cho GMV Max Campaign và Product data, cho phép tạo rules tùy chỉnh với bất kỳ field nào từ các entity `FactGmvMaxCampaignEntity` và `FactGmvMaxProductEntity`.

### Đặc điểm chính

-   **Dynamic Rule Engine**: Hỗ trợ tạo rule cho mọi field của entity
-   **Multi-Entity Support**: Campaign rules, Product rules, và mở rộng cho entity khác
-   **Advanced Template System**: Support Simple, UserType-specific, và Dynamic templates
-   **Dynamic Context Providers**: Thay thế các hard-coded context providers
-   **Visual Rule Builder**: UI trực quan với Syncfusion QueryBuilder
-   **Migration Support**: Migrate seamlessly từ GmvMaxCreativeContextProvider
-   **Backward Compatible**: <PERSON><PERSON><PERSON> h<PERSON><PERSON> mư<PERSON> mà với hệ thống notification hiện tại

---

## 🏗️ Architecture Overview

### Current System Integration

```
Existing Flow:
Data Change → Manual Trigger (by user) → Specific Builder → TikTokNotificationService

Enhanced Flow:
Data Change → Manual Trigger (by user) → Rule Engine → Dynamic Template → TikTokNotificationService (reuse)
```

### Core Components

1. **Dynamic Rule Engine** - Xử lý rule evaluation với JSON conditions
2. **Entity Field Registry** - Metadata system cho dynamic fields
3. **Advanced Template System** - Support multiple template types
4. **Dynamic Context Provider** - Thay thế các hard-coded context providers
5. **Visual Rule Builder** - UI cho business users
6. **Migration Tools** - Migrate từ existing hard-coded systems

---

## 📊 Data Model

### Entities Support

#### FactGmvMaxCampaignEntity (85+ fields)

-   **Performance Metrics**: `ROAS`, `TACOS`, `Cost`, `GrossRevenue`, `Orders`
-   **Campaign Info**: `CampaignName`, `ShoppingAdsType`, `OperationStatus`
-   **Targeting**: `BidType`, `RoasBid`, `TargetRoiBudget`
-   **LIVE Specific**: `LiveViews`, `TtAccountName`, `IdentityId`
-   **Scheduling**: `ScheduleStartTime`, `ScheduleEndTime`

#### FactGmvMaxProductEntity (50+ fields)

-   **Product Performance**: `Orders`, `GrossRevenue`, `ProductClickRate`
-   **Creative Metrics**: `AdConversionRate`, `AdVideoViewRate*`
-   **Product Info**: `ProductName`, `ProductStatus`, `CreativeType`
-   **TikTok Account**: `TtAccountName`, `TtAccountAuthorizationType`

---

## 🛠️ Technical Implementation

### Database Schema

#### Core Entities

**1. Dynamic Rule Entity**

```csharp
public class DynamicNotificationRule : FullAuditedEntity<Guid>
{
    public string RuleName { get; set; }
    public string EntityType { get; set; } // FactGmvMaxCampaign, FactGmvMaxProduct
    public string ConditionsJson { get; set; } // JSON conditions
    public int Priority { get; set; } = 1;
    public bool IsActive { get; set; } = true;

    // Advanced Template System
    public Guid? TemplateId { get; set; }
    public NotificationTemplate Template { get; set; }

    // Dynamic Context Configuration (replaces hard-coded context providers)
    public string ActionUrlTemplate { get; set; } // "/campaigns/{CampaignId}/creatives?tab={Tab}&view={UserType}"
    public string UIComponentType { get; set; } // "CreativeStatusAlert"
    public string CustomActionsJson { get; set; } // JSON array of actions

    // Recipients
    public string RecipientUserIds { get; set; } // JSON array
    public bool UseDefaultUserResolution { get; set; } = true;
}
```

**2. Advanced Template System**

```csharp
public class NotificationTemplate : FullAuditedEntity<Guid>
{
    public string Name { get; set; }
    public string Description { get; set; }
    public NotificationTemplateType Type { get; set; } // Simple, UserTypeSpecific, Dynamic

    // Simple Template
    public string TitleTemplate { get; set; }
    public string ContentTemplate { get; set; }

    // User Type Specific Templates (for migration từ existing system)
    public List<UserTypeTemplate> UserTypeTemplates { get; set; } = new();

    // Dynamic Template Configuration
    public string TemplateEngine { get; set; } = "Simple"; // "Handlebars", "Razor", "Simple"
    public string DefaultVariables { get; set; } // JSON default variables
}

public class UserTypeTemplate : Entity<Guid>
{
    public Guid TemplateId { get; set; }
    public NotificationTemplate Template { get; set; }

    public TikTokUserType UserType { get; set; }
    public string TitleTemplate { get; set; }
    public string ContentTemplate { get; set; }
    public string PayloadTemplate { get; set; } // For context provider data
}

public enum NotificationTemplateType
{
    Simple = 1,           // Single template for all users
    UserTypeSpecific = 2, // Different templates per user type (current system)
    Dynamic = 3           // Fully dynamic with expressions
}
```

### Backend Components

**3. Entity Field Registry**

```csharp
public class EntityFieldRegistry
{
    public static List<FieldMetadata> GetFieldsByEntityType(string entityType)
    {
        return entityType switch
        {
            "FactGmvMaxCampaign" => GetCampaignFields(),
            "FactGmvMaxProduct" => GetProductFields(),
            _ => new List<FieldMetadata>()
        };
    }

    private static List<FieldMetadata> GetCampaignFields()
    {
        return new List<FieldMetadata>
        {
            new("ROAS", "ROAS (Return on Ad Spend)", "decimal", ["LessThan", "GreaterThan", "Between"]),
            new("TACOS", "TACOS (True ACOS %)", "decimal", ["LessThan", "GreaterThan", "Between"]),
            new("Cost", "Chi phí quảng cáo", "decimal", ["LessThan", "GreaterThan", "Between"]),
            new("GrossRevenue", "Tổng doanh thu", "decimal", ["LessThan", "GreaterThan", "Between"]),
            new("Orders", "Số đơn hàng", "int", ["Equal", "LessThan", "GreaterThan"]),
            new("ShoppingAdsType", "Loại campaign", "string", ["Equal", "In"], ["PRODUCT", "LIVE"]),
            new("OperationStatus", "Trạng thái", "string", ["Equal"], ["ENABLE", "DISABLE"]),
            // ... all 85+ fields
        };
    }
}
```

**4. Dynamic Rule Engine**

```csharp
public class DynamicRuleEngine : ITransientDependency
{
    public async Task<List<NotificationRequest>> EvaluateRulesAndBuildNotifications<T>(
        IEnumerable<T> entities,
        string entityType)
    {
        var notifications = new List<NotificationRequest>();
        var activeRules = await _ruleRepository.GetActiveRulesByEntityType(entityType);

        foreach (var rule in activeRules)
        {
            // 1. Evaluate rule conditions
            var matchingEntities = await EvaluateRuleConditions(rule, entities);
            if (!matchingEntities.Any()) continue;

            // 2. Build notifications for each matching entity
            foreach (var entity in matchingEntities)
            {
                var notificationRequests = await BuildNotificationsForEntity(rule, entity);
                notifications.AddRange(notificationRequests);
            }
        }

        return notifications;
    }

    public async Task<List<T>> EvaluateRuleConditions<T>(DynamicNotificationRule rule, IEnumerable<T> entities)
    {
        // Parse JSON conditions to Expression Tree
        var conditions = JsonSerializer.Deserialize<RuleConditions>(rule.ConditionsJson);
        var expression = BuildExpression<T>(conditions);

        // Apply expression to entities
        return entities.Where(expression.Compile()).ToList();
    }

    private Expression<Func<T, bool>> BuildExpression<T>(RuleConditions conditions)
    {
        try
        {
            // Dynamic LINQ expression building
            // Support for complex conditions with AND/OR logic
            return ExpressionBuilder.BuildFromConditions<T>(conditions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error building expression for rule conditions");
            // Return expression that matches nothing to fail safe
            return entity => false;
        }
    }
}
```

**5. Advanced Template Renderer**

```csharp
public class AdvancedTemplateRenderer : ITransientDependency
{
    public async Task<RenderedNotification> RenderNotification<T>(
        NotificationTemplate template,
        T entity,
        TikTokNotificationUserDto recipient)
    {
        return template.Type switch
        {
            NotificationTemplateType.Simple => await RenderSimpleTemplate(template, entity),
            NotificationTemplateType.UserTypeSpecific => await RenderUserTypeTemplate(template, entity, recipient),
            NotificationTemplateType.Dynamic => await RenderDynamicTemplate(template, entity, recipient),
            _ => throw new NotSupportedException($"Template type {template.Type} not supported")
        };
    }

    private async Task<RenderedNotification> RenderUserTypeTemplate<T>(
        NotificationTemplate template,
        T entity,
        TikTokNotificationUserDto recipient)
    {
        // Find user-specific template (migrate existing logic)
        var userTemplate = template.UserTypeTemplates
            .FirstOrDefault(ut => ut.UserType == recipient.UserType);

        if (userTemplate == null)
        {
            // Fallback to default user type
            userTemplate = template.UserTypeTemplates
                .FirstOrDefault(ut => ut.UserType == TikTokUserType.ADVERTISER);
        }

        // Build template data (migrate from existing builder)
        var templateData = await BuildTemplateData(entity, recipient);

        // Render templates
        var title = RenderTemplate(userTemplate.TitleTemplate, templateData);
        var content = RenderTemplate(userTemplate.ContentTemplate, templateData);

        return new RenderedNotification
        {
            Title = title,
            Content = content,
            UserType = recipient.UserType
        };
    }

    private async Task<Dictionary<string, object>> BuildTemplateData<T>(
        T entity,
        TikTokNotificationUserDto recipient)
    {
        var data = new Dictionary<string, object>();

        // Add entity properties
        foreach (var prop in typeof(T).GetProperties())
        {
            data[prop.Name] = prop.GetValue(entity);
        }

        // Add recipient context
        data["UserType"] = recipient.UserType;
        data["UserId"] = recipient.UserId;

        // Add computed values (migrate existing logic)
        if (entity is CampaignStatusSummary summary)
        {
            data["ProblematicCount"] = summary.ProblematicCreativesCount;
            data["TotalCreatives"] = summary.TotalCreatives;
        }

        return data;
    }
}
```

**6. Dynamic Context Provider (Replaces GmvMaxCreativeContextProvider)**

```csharp
public class DynamicContextProvider : ITransientDependency
{
    public async Task<DynamicNotificationContext> BuildDynamicContext<T>(
        DynamicNotificationRule rule,
        T entity,
        TikTokNotificationUserDto recipient)
    {
        // Build dynamic URL from template
        var actionUrl = await BuildActionUrl(rule.ActionUrlTemplate, entity, recipient);

        // Build dynamic UI component
        var uiComponent = BuildUIComponent(rule.UIComponentType, entity, recipient, rule.CustomActionsJson);

        // Build payload (migrate from existing builder logic)
        var payload = await BuildPayload(entity, recipient);

        return new DynamicNotificationContext
        {
            ContextName = $"DynamicRule_{rule.Id}",
            ActionUrl = actionUrl,
            UIComponent = uiComponent,
            Payload = payload
        };
    }

    private async Task<string> BuildActionUrl<T>(string urlTemplate, T entity, TikTokNotificationUserDto recipient)
    {
        if (string.IsNullOrEmpty(urlTemplate))
        {
            // Fallback to default URL based on entity type
            return GetDefaultActionUrl(typeof(T).Name, GetEntityId(entity));
        }

        // Replace placeholders: {CampaignId}, {UserType}, {Tab}, etc.
        var url = urlTemplate;

        // Entity properties
        foreach (var prop in typeof(T).GetProperties())
        {
            url = url.Replace($"{{{prop.Name}}}", prop.GetValue(entity)?.ToString() ?? "");
        }

        // User context
        url = url.Replace("{UserType}", recipient.UserType.ToString().ToLower());
        url = url.Replace("{UserId}", recipient.UserId);

        // Business logic for tab/view based on user type (migrate existing logic)
        url = ApplyUserTypeSpecificRouting(url, recipient.UserType);

        return url;
    }

    private string ApplyUserTypeSpecificRouting(string url, TikTokUserType userType)
    {
        // Migrate logic from GmvMaxCreativeContextProvider
        if (url.Contains("{Tab}") || url.Contains("{View}"))
        {
            var (tab, view) = userType switch
            {
                TikTokUserType.CAMPAIGN_MANAGER => ("disabled", "manager"),
                TikTokUserType.CREATIVE_APPROVER => ("review", "approver"),
                TikTokUserType.ADVERTISER => ("performance", "advertiser"),
                _ => ("creatives", "default")
            };

            url = url.Replace("{Tab}", tab).Replace("{View}", view);
        }

        return url;
    }
}
```

---

## 🚨 Notification Trigger Points

### User Manual Trigger

```csharp
// Bạn sẽ gọi service này từ bất kỳ đâu khi cần
public class NotificationTriggerService : ITransientDependency
{
    public async Task TriggerCampaignRuleCheck(string campaignId)
    {
        var campaign = await _campaignRepository.GetAsync(campaignId);
        await _ruleEngine.EvaluateRulesAndBuildNotifications(new[] { campaign }, "FactGmvMaxCampaign");
    }

    public async Task TriggerProductRuleCheck(List<string> productIds)
    {
        var products = await _productRepository.GetListAsync(p => productIds.Contains(p.ProductId));
        await _ruleEngine.EvaluateRulesAndBuildNotifications(products, "FactGmvMaxProduct");
    }

    public async Task TriggerBulkRuleCheck<T>(IEnumerable<T> entities, string entityType)
    {
        await _ruleEngine.EvaluateRulesAndBuildNotifications(entities, entityType);
    }
}
```

### Usage Examples

```csharp
// Trong Dashboard Controller
public async Task<IActionResult> GetDashboard()
{
    var campaigns = await GetRecentCampaigns();

    // Trigger rule check khi user xem dashboard
    await _notificationTrigger.TriggerBulkRuleCheck(campaigns, "FactGmvMaxCampaign");

    return View(dashboardData);
}

// Trong Data Sync Service
public async Task SyncCampaignData()
{
    var syncedCampaigns = await SyncFromTikTokAPI();

    // Trigger rule check sau khi sync data
    await _notificationTrigger.TriggerBulkRuleCheck(syncedCampaigns, "FactGmvMaxCampaign");
}
```

---

## 🔄 Migration Strategy từ GmvMaxCreativeContextProvider

### Phase 1: Parallel Implementation

#### Migration Mapping

```csharp
public class GmvMaxCreativeContextProviderMigration
{
    public async Task<NotificationTemplate> MigrateToTemplate()
    {
        // Create UserTypeSpecific template from existing hard-coded templates
        var template = new NotificationTemplate
        {
            Name = "GMV Max Creative Status Change (Migrated)",
            Type = NotificationTemplateType.UserTypeSpecific,
            UserTypeTemplates = new List<UserTypeTemplate>
            {
                new()
                {
                    UserType = TikTokUserType.CAMPAIGN_MANAGER,
                    TitleTemplate = "[GMV Max] {CampaignName}: {ProblematicCount} video lỗi",
                    ContentTemplate = "Campaign {CampaignName} có {ProblematicCount} videos bị vô hiệu hóa. Cần kiểm tra và xử lý ngay.",
                    PayloadTemplate = JsonSerializer.Serialize(new { UserType = "CAMPAIGN_MANAGER" })
                },
                new()
                {
                    UserType = TikTokUserType.CREATIVE_APPROVER,
                    TitleTemplate = "[GMV Max] {CampaignName}: {ProblematicCount} video cần review",
                    ContentTemplate = "Campaign {CampaignName} có {ProblematicCount} videos cần được review và approve.",
                    PayloadTemplate = JsonSerializer.Serialize(new { UserType = "CREATIVE_APPROVER" })
                },
                // ... other user types
            }
        };

        return template;
    }

    public async Task<DynamicNotificationRule> MigrateToRule(NotificationTemplate template)
    {
        return new DynamicNotificationRule
        {
            RuleName = "Creative Status Change Alert (Migrated)",
            EntityType = "FactGmvMaxProduct",
            TemplateId = template.Id,
            ActionUrlTemplate = "/campaigns/{CampaignId}/creatives?tab={Tab}&view={UserType}",
            UIComponentType = "CreativeStatusAlert",
            UseDefaultUserResolution = true,
            ConditionsJson = JsonSerializer.Serialize(new
            {
                condition = "or",
                rules = new[]
                {
                    new { field = "CreativeDeliveryStatus", @operator = "equal", value = "NOT_DELIVERYIN" },
                    new { field = "CreativeDeliveryStatus", @operator = "equal", value = "EXCLUDED" },
                    new { field = "CreativeDeliveryStatus", @operator = "equal", value = "UNAVAILABLE" },
                    new { field = "CreativeDeliveryStatus", @operator = "equal", value = "REJECTED" }
                }
            })
        };
    }
}
```

### Phase 2: Gradual Transition

#### Feature Flag Configuration

```json
{
    "Features": {
        "DynamicRules": {
            "GmvMaxCreativeStatusChange": false, // Start with legacy
            "NewCampaignRules": true // New rules use dynamic system
        }
    }
}
```

#### Hybrid Service

```csharp
public class HybridNotificationService : ITransientDependency
{
    public async Task<bool> SendNotification(string context, string objectId, object data)
    {
        // Feature flag cho gradual migration
        var useDynamicRules = _config.GetValue<bool>($"Features:DynamicRules:{context}");

        if (useDynamicRules)
        {
            // Use new dynamic rule system
            return await _dynamicRuleEngine.EvaluateAndNotify(context, objectId, data);
        }
        else
        {
            // Fall back to legacy system
            return await _legacyNotificationService.SendCampaignNotificationAsync(objectId, context);
        }
    }
}
```

---

## 🎨 User Interface Design

### Rule Management Dashboard

```html
<!-- Add Campaign Rules tab -->
<ul class="nav nav-tabs">
    <li class="nav-item">
        <a class="nav-link active" href="#balance-rules">Balance Rules</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="#campaign-rules">Campaign Rules</a>
    </li>
</ul>

<!-- Campaign Rules Tab Content -->
<div class="tab-pane" id="campaign-rules">
    <div class="d-flex justify-content-between mb-3">
        <div class="entity-type-selector">
            <label>Chọn loại dữ liệu:</label>
            <select class="form-select" id="entityTypeSelector">
                <option value="FactGmvMaxCampaign">GMV Max Campaign</option>
                <option value="FactGmvMaxProduct">GMV Max Product</option>
            </select>
        </div>
        <div>
            <button class="btn btn-primary" onclick="openCreateRuleModal()">
                <i class="fas fa-plus"></i> Tạo Rule Mới
            </button>
            <button class="btn btn-secondary" onclick="openTemplateModal()">
                <i class="fas fa-file-alt"></i> Quản lý Templates
            </button>
        </div>
    </div>

    <!-- Rules Table -->
    <table id="CampaignRulesTable" class="table table-striped">
        <thead>
            <tr>
                <th>Tên Rule</th>
                <th>Entity Type</th>
                <th>Template</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
    </table>
</div>
```

### Rule Creation Modal

```html
<div class="modal fade" id="createCampaignRuleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tạo Campaign Rule Mới</h5>
            </div>
            <div class="modal-body">
                <!-- Basic Info -->
                <div class="row mb-3">
                    <div class="col-6">
                        <label>Tên Rule</label>
                        <input type="text" class="form-control" id="ruleName" />
                    </div>
                    <div class="col-6">
                        <label>Entity Type</label>
                        <select class="form-select" id="entityType">
                            <option value="FactGmvMaxCampaign">
                                GMV Max Campaign
                            </option>
                            <option value="FactGmvMaxProduct">
                                GMV Max Product
                            </option>
                        </select>
                    </div>
                </div>

                <!-- QueryBuilder -->
                <div class="mb-3">
                    <label>Điều kiện Rule</label>
                    <div id="campaignQueryBuilder"></div>
                </div>

                <!-- Template Selection -->
                <div class="mb-3">
                    <label>Template</label>
                    <select class="form-select" id="templateSelector">
                        <option value="">Chọn template...</option>
                        <!-- Templates will be loaded dynamically -->
                    </select>
                </div>

                <!-- Context Configuration -->
                <div class="mb-3">
                    <label>Action URL Template</label>
                    <input
                        type="text"
                        class="form-control"
                        id="actionUrlTemplate"
                        placeholder="/campaigns/{CampaignId}/creatives?tab={Tab}&view={UserType}"
                    />

                    <label class="mt-2">UI Component Type</label>
                    <input
                        type="text"
                        class="form-control"
                        id="uiComponentType"
                        placeholder="CreativeStatusAlert"
                    />
                </div>
            </div>

            <div class="modal-footer">
                <button
                    type="button"
                    class="btn btn-secondary"
                    data-bs-dismiss="modal"
                >
                    Hủy
                </button>
                <button type="button" class="btn btn-primary" id="saveRuleBtn">
                    Lưu Rule
                </button>
            </div>
        </div>
    </div>
</div>
```

### Template Management Modal

```html
<div class="modal fade" id="templateManagementModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Quản lý Templates</h5>
            </div>
            <div class="modal-body">
                <!-- Template Type Selection -->
                <div class="mb-3">
                    <label>Template Type</label>
                    <select class="form-select" id="templateType">
                        <option value="Simple">Simple Template</option>
                        <option value="UserTypeSpecific">
                            User Type Specific
                        </option>
                        <option value="Dynamic">Dynamic Template</option>
                    </select>
                </div>

                <!-- Simple Template -->
                <div id="simpleTemplate" class="template-section">
                    <div class="mb-3">
                        <label>Title Template</label>
                        <input
                            type="text"
                            class="form-control"
                            id="simpleTitleTemplate"
                            placeholder="🚨 Campaign {CampaignName}: {IssueDescription}"
                        />
                    </div>
                    <div class="mb-3">
                        <label>Content Template</label>
                        <textarea
                            class="form-control"
                            id="simpleContentTemplate"
                            rows="3"
                            placeholder="Campaign {CampaignName} có vấn đề {IssueType}"
                        ></textarea>
                    </div>
                </div>

                <!-- User Type Specific Template -->
                <div
                    id="userTypeTemplate"
                    class="template-section"
                    style="display: none;"
                >
                    <div class="user-type-templates">
                        <!-- Dynamic user type template forms -->
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button
                    type="button"
                    class="btn btn-secondary"
                    data-bs-dismiss="modal"
                >
                    Hủy
                </button>
                <button
                    type="button"
                    class="btn btn-primary"
                    id="saveTemplateBtn"
                >
                    Lưu Template
                </button>
            </div>
        </div>
    </div>
</div>
```

---

## 📋 Implementation Plan (4-5 weeks)

### Week 1-2: Foundation & Database

-   [ ] **Database Schema**: Create all entities (DynamicNotificationRule, NotificationTemplate, UserTypeTemplate)
-   [ ] **Entity Framework**: Setup repositories and DbContext
-   [ ] **Basic Rule Engine**: Core condition evaluation logic
-   [ ] **Field Registry**: Metadata system for FactGmvMaxCampaign/Product fields

### Week 3: Advanced Template System

-   [ ] **Template Renderer**: Support for Simple, UserTypeSpecific, Dynamic templates
-   [ ] **Migration Templates**: Convert existing hard-coded templates to database
-   [ ] **Template Data Builder**: Extract data for template rendering
-   [ ] **Template Testing**: Unit tests for all template types

### Week 4: Dynamic Context Provider & Migration

-   [ ] **Context Provider**: Replace GmvMaxCreativeContextProvider with dynamic system
-   [ ] **URL Builder**: Dynamic URL generation with placeholders
-   [ ] **UI Component Builder**: Dynamic UI component configuration
-   [ ] **Migration Logic**: Seamless transition from hard-coded to dynamic
-   [ ] **Hybrid Service**: Support both legacy and dynamic systems

### Week 5: Frontend & Integration

-   [ ] **Advanced Rule Builder UI**: Support for templates, context configuration
-   [ ] **Template Management UI**: Create/edit templates with user type support
-   [ ] **Migration Tools**: Tools to migrate existing rules
-   [ ] **End-to-end Testing**: Complete notification flow testing

---

## 📚 Configuration Examples

### Sample Rule Configurations

#### 1. Low ROAS Campaign Alert

```json
{
    "ruleName": "Low ROAS Alert - Critical",
    "entityType": "FactGmvMaxCampaign",
    "conditions": {
        "condition": "and",
        "rules": [
            {
                "field": "ROAS",
                "operator": "lessthan",
                "value": "2.0"
            },
            {
                "field": "Cost",
                "operator": "greaterthan",
                "value": "100"
            }
        ]
    },
    "templateId": "low-roas-template-id",
    "actionUrlTemplate": "/campaigns/{CampaignId}?alert=low-roas",
    "uiComponentType": "CampaignAlert"
}
```

#### 2. Creative Status Change (Migrated from GmvMaxCreativeContextProvider)

```json
{
    "ruleName": "Creative Status Change Alert (Migrated)",
    "entityType": "FactGmvMaxProduct",
    "conditions": {
        "condition": "or",
        "rules": [
            {
                "field": "CreativeDeliveryStatus",
                "operator": "equal",
                "value": "NOT_DELIVERYIN"
            },
            {
                "field": "CreativeDeliveryStatus",
                "operator": "equal",
                "value": "EXCLUDED"
            }
        ]
    },
    "templateId": "creative-status-template-id",
    "actionUrlTemplate": "/campaigns/{CampaignId}/creatives?tab={Tab}&view={UserType}",
    "uiComponentType": "CreativeStatusAlert"
}
```

---

## 🎯 Key Benefits

1. **Future-Proof Database Schema**: Không cần refactor sau này
2. **Complete Migration Path**: Seamless transition từ GmvMaxCreativeContextProvider
3. **Advanced Template System**: Support mọi loại template từ simple đến dynamic
4. **Dynamic Context Providers**: Flexible URL routing và UI components
5. **Backward Compatible**: Existing notification system vẫn hoạt động
6. **User-Friendly**: Visual rule builder với QueryBuilder

---

## 🎯 Success Criteria

### Technical Metrics

-   [ ] **Performance**: Rule evaluation < 2s per rule per batch
-   [ ] **Scalability**: Support 50+ active rules simultaneously
-   [ ] **Reliability**: 99.9% uptime for rule evaluation service
-   [ ] **Migration Success**: 100% functional parity với GmvMaxCreativeContextProvider
-   [ ] **Backward Compatibility**: 0 breaking changes during migration

### Business Metrics

-   [ ] **User Adoption**: 50% of active users create at least 1 rule
-   [ ] **Notification Relevance**: < 10% user complaints about irrelevant notifications
-   [ ] **Issue Response Time**: 30% improvement in issue detection speed
-   [ ] **Template Usage**: 80% of rules use advanced template features

### User Experience Metrics

-   [ ] **Ease of Use**: 70% of users can create a rule without training
-   [ ] **Interface Satisfaction**: 4.0/5 average rating for rule builder UI
-   [ ] **Feature Completeness**: Support for all FactGmvMaxCampaign fields (85+)
-   [ ] **Mobile Compatibility**: Full functionality on mobile devices

---

_Last Updated: [Current Date]_  
_Version: 2.0_  
_Status: Ready for Implementation_
