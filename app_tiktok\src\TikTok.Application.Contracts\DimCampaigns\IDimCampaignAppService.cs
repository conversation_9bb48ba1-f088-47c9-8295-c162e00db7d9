using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DimCampaigns
{
    /// <summary>
    /// Service interface for DimCampaign operations
    /// </summary>
    public interface IDimCampaignAppService : IApplicationService
    {
        /// <summary>
        /// Get all active campaigns for dropdown/multiselect
        /// </summary>
        /// <returns>List of active campaigns</returns>
        Task<List<DimCampaignDto>> GetActiveCampaignsAsync();

        /// <summary>
        /// Get campaigns by type
        /// </summary>
        /// <param name="campaignType">Campaign type</param>
        /// <returns>List of campaigns of specified type</returns>
        Task<List<DimCampaignDto>> GetCampaignsByTypeAsync(string campaignType);

        /// <summary>
        /// Get campaigns by advertiser
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>List of campaigns for specified advertiser</returns>
        Task<List<DimCampaignDto>> GetCampaignsByAdvertiserAsync(string advertiserId);
    }
}
