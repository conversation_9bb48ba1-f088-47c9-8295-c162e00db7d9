using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.Contracts.Notification
{
    /// <summary>
    /// Service chung để test tất cả loại notification system
    /// </summary>
    public interface INotificationTestService : ITransientDependency
    {
        /// <summary>
        /// Test notification với data thật nhưng giới hạn phạm vi
        /// </summary>
        Task<NotificationTestResult> TestNotificationAsync(
            string campaignId, 
            string notificationType = "GmvMaxCreativeStatusChange",
            int maxDays = 1);
    }
}

