using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.RawGmvMaxProductCreativeReports
{
    /// <summary>
    /// DTO cho RawGmvMaxProductCreativeReportEntity
    /// </summary>
    public class RawGmvMaxProductCreativeReportDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID Business Center
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID TikTok Shop
        /// </summary>
        public string StoreId { get; set; }

        /// <summary>
        /// ID chiến dịch Product GMV Max
        /// </summary>
        public string CampaignId { get; set; }

        /// <summary>
        /// ID sản phẩm SPU
        /// </summary>
        public string ItemGroupId { get; set; }

        /// <summary>
        /// ID TikTok post (có thể là -1 nếu là Product Card)
        /// </summary>
        public string ItemId { get; set; }

        /// <summary>
        /// Loại creative (ADS_AND_ORGANIC, ORGANIC, REMOVED)
        /// </summary>
        public CreativeType CreativeType { get; set; }

        /// <summary>
        /// Tên creative
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// Tên TikTok account (có thể là 0 hoặc -1 nếu thiếu quyền)
        /// </summary>
        public string? TtAccountName { get; set; }

        /// <summary>
        /// URL hình đại diện TikTok account
        /// </summary>
        public string? TtAccountProfileImageUrl { get; set; }

        /// <summary>
        /// Loại ủy quyền (TTS_TT, AFFILIATE, TT_USER, BC_AUTH_TT, AUTH_CODE, UNSET)
        /// </summary>
        public TtAccountAuthorizationType? TtAccountAuthorizationType { get; set; }

        /// <summary>
        /// Loại nội dung shop (VIDEO, PRODUCT_CARD, LIVE)
        /// </summary>
        public ShopContentType? ShopContentType { get; set; }

        /// <summary>
        /// Số lượng đơn hàng SKU từ creative này
        /// </summary>
        public int? Orders { get; set; }

        /// <summary>
        /// Tổng doanh thu gộp từ creative này
        /// </summary>
        public decimal? GrossRevenue { get; set; }

        /// <summary>
        /// Tổng lượt hiển thị sản phẩm (organic + paid)
        /// </summary>
        public long? ProductImpressions { get; set; }

        /// <summary>
        /// Tổng lượt click sản phẩm (organic + paid)
        /// </summary>
        public long? ProductClicks { get; set; }

        /// <summary>
        /// Tỷ lệ click sản phẩm (ProductClicks/ProductImpressions)
        /// </summary>
        public decimal? ProductClickRate { get; set; }

        /// <summary>
        /// Tỷ lệ click-through của paid views từ video này
        /// </summary>
        public decimal? AdClickRate { get; set; }

        /// <summary>
        /// Tỷ lệ chuyển đổi của paid clicks từ video này
        /// </summary>
        public decimal? AdConversionRate { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 2 giây
        /// </summary>
        public decimal? AdVideoViewRate2s { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 6 giây
        /// </summary>
        public decimal? AdVideoViewRate6s { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 25% thời lượng
        /// </summary>
        public decimal? AdVideoViewRateP25 { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 50% thời lượng
        /// </summary>
        public decimal? AdVideoViewRateP50 { get; set; }

        /// <summary>
        /// Tỷ lệ xem video ít nhất 75% thời lượng
        /// </summary>
        public decimal? AdVideoViewRateP75 { get; set; }

        /// <summary>
        /// Tỷ lệ xem video 100% thời lượng
        /// </summary>
        public decimal? AdVideoViewRateP100 { get; set; }

        /// <summary>
        /// Tiền tệ - Mã tiền tệ, ví dụ: USD
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Ngày giờ tổng hợp báo cáo, UTC format yyyy-MM-dd HH:00:00 (theo giờ)
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Tỷ suất lợi nhuận (Return on Investment)
        /// </summary>
        public decimal? ROI { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng
        /// </summary>
        public decimal? CostPerOrder { get; set; }

        /// <summary>
        /// Chi phí
        /// </summary>
        public decimal Cost { get; set; }

        /// <summary>
        /// Trạng thái giao hàng creative
        /// </summary>
        public CreativeDeliveryStatus CreativeDeliveryStatus { get; set; }
    }
}
