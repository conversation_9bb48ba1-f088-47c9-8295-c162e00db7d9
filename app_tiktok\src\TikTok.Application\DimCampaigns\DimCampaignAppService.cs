using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using TikTok.Entities;

namespace TikTok.DimCampaigns
{
    /// <summary>
    /// Application service for DimCampaign operations
    /// </summary>
    public class DimCampaignAppService : ApplicationService, IDimCampaignAppService
    {
        private readonly IRepository<DimCampaignEntity, Guid> _dimCampaignRepository;

        public DimCampaignAppService(IRepository<DimCampaignEntity, Guid> dimCampaignRepository)
        {
            _dimCampaignRepository = dimCampaignRepository;
        }

        /// <summary>
        /// Get all active campaigns for dropdown/multiselect
        /// </summary>
        /// <returns>List of active campaigns</returns>
        public async Task<List<DimCampaignDto>> GetActiveCampaignsAsync()
        {
            var query = await _dimCampaignRepository.GetQueryableAsync();
            
            var activeCampaigns = query
                .Where(c => c.OperationStatus == "ENABLE" && c.IsCurrent == true) // Current and active records only
                .OrderBy(c => c.CampaignName)
                .Select(c => new DimCampaignDto
                {
                    Id = c.Id,
                    CampaignId = c.CampaignId,
                    DimAdAccountId = c.DimAdAccountId,
                    AdvertiserId = c.AdvertiserId,
                    CampaignName = c.CampaignName,
                    ObjectiveType = c.ObjectiveType,
                    CampaignType = c.CampaignType,
                    BudgetMode = c.BudgetMode,
                    Budget = c.Budget,
                    OperationStatus = c.OperationStatus,
                    CreateTime = c.CreateTime,
                    ModifyTime = c.ModifyTime,
                    IsCurrent = c.IsCurrent,
                    EffectiveDate = c.EffectiveDate,
                    ExpirationDate = c.ExpirationDate,
                    RowVersion = c.RowVersion
                })
                .ToList();

            return activeCampaigns;
        }

        /// <summary>
        /// Get campaigns by type
        /// </summary>
        /// <param name="campaignType">Campaign type</param>
        /// <returns>List of campaigns of specified type</returns>
        public async Task<List<DimCampaignDto>> GetCampaignsByTypeAsync(string campaignType)
        {
            var query = await _dimCampaignRepository.GetQueryableAsync();
            
            var campaigns = query
                .Where(c => c.OperationStatus == "ENABLE" && 
                           c.IsCurrent == true && 
                           c.CampaignType == campaignType)
                .OrderBy(c => c.CampaignName)
                .Select(c => new DimCampaignDto
                {
                    Id = c.Id,
                    CampaignId = c.CampaignId,
                    DimAdAccountId = c.DimAdAccountId,
                    AdvertiserId = c.AdvertiserId,
                    CampaignName = c.CampaignName,
                    ObjectiveType = c.ObjectiveType,
                    CampaignType = c.CampaignType,
                    BudgetMode = c.BudgetMode,
                    Budget = c.Budget,
                    OperationStatus = c.OperationStatus,
                    CreateTime = c.CreateTime,
                    ModifyTime = c.ModifyTime,
                    IsCurrent = c.IsCurrent,
                    EffectiveDate = c.EffectiveDate,
                    ExpirationDate = c.ExpirationDate,
                    RowVersion = c.RowVersion
                })
                .ToList();

            return campaigns;
        }

        /// <summary>
        /// Get campaigns by advertiser
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>List of campaigns for specified advertiser</returns>
        public async Task<List<DimCampaignDto>> GetCampaignsByAdvertiserAsync(string advertiserId)
        {
            var query = await _dimCampaignRepository.GetQueryableAsync();
            
            var campaigns = query
                .Where(c => c.OperationStatus == "ENABLE" && 
                           c.IsCurrent == true && 
                           c.AdvertiserId == advertiserId)
                .OrderBy(c => c.CampaignName)
                .Select(c => new DimCampaignDto
                {
                    Id = c.Id,
                    CampaignId = c.CampaignId,
                    DimAdAccountId = c.DimAdAccountId,
                    AdvertiserId = c.AdvertiserId,
                    CampaignName = c.CampaignName,
                    ObjectiveType = c.ObjectiveType,
                    CampaignType = c.CampaignType,
                    BudgetMode = c.BudgetMode,
                    Budget = c.Budget,
                    OperationStatus = c.OperationStatus,
                    CreateTime = c.CreateTime,
                    ModifyTime = c.ModifyTime,
                    IsCurrent = c.IsCurrent,
                    EffectiveDate = c.EffectiveDate,
                    ExpirationDate = c.ExpirationDate,
                    RowVersion = c.RowVersion
                })
                .ToList();

            return campaigns;
        }
    }
}
