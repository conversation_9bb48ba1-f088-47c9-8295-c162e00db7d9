using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using TikTok.Entities.Dim;

namespace TikTok.DimStores
{
    /// <summary>
    /// Application service for DimStore operations
    /// </summary>
    public class DimStoreAppService : ApplicationService, IDimStoreAppService
    {
        private readonly IRepository<DimStoreEntity, Guid> _dimStoreRepository;

        public DimStoreAppService(IRepository<DimStoreEntity, Guid> dimStoreRepository)
        {
            _dimStoreRepository = dimStoreRepository;
        }

        /// <summary>
        /// Get all active stores for dropdown/multiselect
        /// </summary>
        /// <returns>List of active stores</returns>
        public async Task<List<DimStoreDto>> GetActiveStoresAsync()
        {
            var query = await _dimStoreRepository.GetQueryableAsync();
            
            var activeStores = query
                .Where(s => s.Status == "ACTIVE" && s.EffectiveEndDate == null) // Current records only
                .OrderBy(s => s.StoreName)
                .Select(s => new DimStoreDto
                {
                    Id = s.Id,
                    StoreId = s.StoreId,
                    StoreName = s.StoreName,
                    StoreDescription = s.Description,
                    StoreType = s.StoreType,
                    Status = s.Status,
                    Country = s.Country,
                    Currency = null, // Not in entity, can be added if needed
                    Timezone = null, // Not in entity, can be added if needed
                    CreatedDate = s.CreatedAt,
                    LastModifiedDate = s.UpdatedAt
                })
                .ToList();

            return activeStores;
        }

        /// <summary>
        /// Get stores by country
        /// </summary>
        /// <param name="country">Country code</param>
        /// <returns>List of stores in specified country</returns>
        public async Task<List<DimStoreDto>> GetStoresByCountryAsync(string country)
        {
            var query = await _dimStoreRepository.GetQueryableAsync();
            
            var stores = query
                .Where(s => s.Status == "ACTIVE" && 
                           s.EffectiveEndDate == null && 
                           s.Country == country)
                .OrderBy(s => s.StoreName)
                .Select(s => new DimStoreDto
                {
                    Id = s.Id,
                    StoreId = s.StoreId,
                    StoreName = s.StoreName,
                    StoreDescription = s.Description,
                    StoreType = s.StoreType,
                    Status = s.Status,
                    Country = s.Country,
                    Currency = null,
                    Timezone = null,
                    CreatedDate = s.CreatedAt,
                    LastModifiedDate = s.UpdatedAt
                })
                .ToList();

            return stores;
        }
    }
}
