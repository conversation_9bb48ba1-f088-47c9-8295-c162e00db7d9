using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.ResourcePermissions;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Users;

namespace TikTok.FactGmvMaxCampaigns
{
    /// <summary>
    /// Helper class để định nghĩa các fields được phép hiển thị theo từng quyền và quản lý quyền truy cập AdAccount
    /// </summary>
    public class PermissionFieldHelper : ITransientDependency
    {
        private readonly IResourcePermissionAppService _resourcePermissionAppService;
        private readonly ICurrentUser _currentUser;

        public PermissionFieldHelper(
            IResourcePermissionAppService resourcePermissionAppService,
            ICurrentUser currentUser)
        {
            _resourcePermissionAppService = resourcePermissionAppService;
            _currentUser = currentUser;
        }

        /// <summary>
        /// L<PERSON>y danh sách AdvertiserId được phân quyền cho user hiện tại
        /// </summary>
        /// <returns>Danh sách AdvertiserId đư<PERSON><PERSON> phân quyền, null nếu là admin (có thể xem tất cả)</returns>
        public async Task<List<string>?> GetUserAllowedAdvertiserIdsAsync()
        {
            // Kiểm tra nếu user là admin
            if (IsAdminUser())
            {
                return null; // Admin có thể xem tất cả dữ liệu
            }

            var userId = _currentUser.Id;
            if (userId == null)
            {
                return new List<string>(); // Không có user ID, không có quyền gì
            }

            var permissions = await _resourcePermissionAppService.GetByUserIdAsync(userId.Value);
            var advertiserIds = permissions.Items
                .Where(p => p.ResourceType == "AdAccount")
                .Select(p => p.ResourceId)
                .Distinct()
                .ToList();

            return advertiserIds;
        }

        /// <summary>
        /// Lấy danh sách AdvertiserId được phân quyền cho user hiện tại
        /// Có thể bypass nếu user có quyền ViewAllAdvertisers
        /// </summary>
        /// <param name="hasViewAllAdvertisers">User có quyền ViewAllAdvertisers không</param>
        /// <returns>Danh sách AdvertiserId được phân quyền, null nếu có thể xem tất cả</returns>
        public async Task<List<string>?> GetUserAllowedAdvertiserIdsAsync(bool hasViewAllAdvertisers)
        {
            // Nếu có quyền ViewAllAdvertisers, bypass tất cả filter
            if (hasViewAllAdvertisers)
            {
                return null; // Có thể xem tất cả dữ liệu
            }

            // Sử dụng logic hiện tại
            return await GetUserAllowedAdvertiserIdsAsync();
        }

        /// <summary>
        /// Kiểm tra xem user hiện tại có phải là admin không
        /// </summary>
        /// <returns>True nếu là admin</returns>
        public bool IsAdminUser()
        {
            // ✅ Kiểm tra admin theo role - đơn giản và hiệu quả
            return _currentUser.Roles.Contains("admin");
        }

        /// <summary>
        /// Tạo SQL WHERE clause để filter theo AdvertiserId
        /// </summary>
        /// <param name="allowedAdvertiserIds">Danh sách AdvertiserId được phép, null nếu admin</param>
        /// <param name="tableAlias">Alias của bảng trong SQL (ví dụ: "fc", "fp")</param>
        /// <returns>SQL WHERE clause hoặc chuỗi rỗng nếu admin</returns>
        public string GetAdvertiserIdFilterClause(List<string>? allowedAdvertiserIds, string tableAlias = "fc")
        {
            if (allowedAdvertiserIds == null)
            {
                return ""; // Admin - có thể xem tất cả dữ liệu
            }
            
            if (!allowedAdvertiserIds.Any())
            {
                return $"AND 1=0"; // User không có quyền gì - không thể xem dữ liệu nào
            }

            var advertiserIdList = string.Join(",", allowedAdvertiserIds.Select(id => $"'{id}'"));
            return $"AND {tableAlias}.AdvertiserId IN ({advertiserIdList})";
        }

        /// <summary>
        /// Tạo SQL parameter cho AdvertiserId filter
        /// </summary>
        /// <param name="allowedAdvertiserIds">Danh sách AdvertiserId được phép</param>
        /// <returns>Object chứa parameter cho Dapper</returns>
        public object GetAdvertiserIdFilterParameter(List<string>? allowedAdvertiserIds)
        {
            if (allowedAdvertiserIds == null || !allowedAdvertiserIds.Any())
            {
                return new { AllowedAdvertiserIds = new List<string>() };
            }

            return new { AllowedAdvertiserIds = allowedAdvertiserIds };
        }

        /// <summary>
        /// Các fields liên quan đến chi tiêu của tài khoản quảng cáo
        /// </summary>
        public static readonly HashSet<string> SpendingFields = new HashSet<string>
        {
            // Chi phí quảng cáo
            "Cost", "CostVND", "CostUSD",
            "NetCost", "NetCostVND", "NetCostUSD",
            
            // Chi phí mỗi đơn hàng
            "CostPerOrder", "CostPerOrderVND", "CostPerOrderUSD",
            
            // Ngân sách
            "TargetRoiBudget", "TargetRoiBudgetVND", "TargetRoiBudgetUSD",
            "MaxDeliveryBudget", "MaxDeliveryBudgetVND", "MaxDeliveryBudgetUSD",
            
            // Mục tiêu ROAS
            "RoasBid", "RoasBidVND", "RoasBidUSD",
            
            // Chi phí LIVE (chỉ có cho LIVE campaigns)
            "CostPerLiveView", "CostPerLiveViewVND", "CostPerLiveViewUSD",
            "CostPerTenSecondLiveView", "CostPerTenSecondLiveViewVND", "CostPerTenSecondLiveViewUSD"
        };

        /// <summary>
        /// Các fields liên quan đến chỉ số quảng cáo
        /// </summary>
        public static readonly HashSet<string> MetricsFields = new HashSet<string>
        {
            // Số lượng đơn hàng
            "Orders",
            
            // Doanh thu
            "GrossRevenue", "GrossRevenueVND", "GrossRevenueUSD",
            
            // Hiệu quả quảng cáo
            "ROAS",
            
            // Chỉ số LIVE (chỉ có cho LIVE campaigns)
            "LiveViews", "TenSecondLiveViews", "LiveFollows",
            
            // Thông tin TikTok account (chỉ có cho LIVE campaigns)
            "TtAccountName", "TtAccountProfileImageUrl", "IdentityId"
        };

        /// <summary>
        /// Các fields chỉ được hiển thị khi có quyền ViewAll hoặc ViewAllAdvertisers
        /// </summary>
        public static readonly HashSet<string> RestrictedFields = new HashSet<string>
        {
            // TACOS - chỉ hiển thị cho admin hoặc user có quyền xem tất cả
            "TACOS"
        };

        /// <summary>
        /// Các fields cơ bản luôn được hiển thị (không liên quan đến chi tiêu hay chỉ số)
        /// </summary>
        public static readonly HashSet<string> BasicFields = new HashSet<string>
        {
            // Business Keys
            "CampaignId", "StoreId", "BcId", "AdvertiserId",
            
            // Thông tin cơ bản
            "CampaignName", "ShoppingAdsType", "OperationStatus", "BidType",
            
            // Cấu hình chiến dịch
            "ScheduleType", "ScheduleStartTime", "ScheduleEndTime",
            
            // Thông tin hệ thống
            "Currency", "Date", "DimDateId", "DimBusinessCenterId", 
            "DimAdAccountId", "DimCampaignId", "DimStoreId"
        };

        /// <summary>
        /// Kiểm tra xem field có được phép hiển thị với quyền ViewSpending không
        /// </summary>
        public static bool IsSpendingField(string fieldName)
        {
            return SpendingFields.Contains(fieldName);
        }

        /// <summary>
        /// Kiểm tra xem field có được phép hiển thị với quyền ViewMetrics không
        /// </summary>
        public static bool IsMetricsField(string fieldName)
        {
            return MetricsFields.Contains(fieldName);
        }

        /// <summary>
        /// Kiểm tra xem field có được phép hiển thị với quyền ViewAll không
        /// </summary>
        public static bool IsViewAllField(string fieldName)
        {
            return BasicFields.Contains(fieldName) || 
                   SpendingFields.Contains(fieldName) || 
                   MetricsFields.Contains(fieldName) ||
                   RestrictedFields.Contains(fieldName);
        }

        /// <summary>
        /// Kiểm tra xem field có phải là field bị hạn chế không (chỉ hiển thị với ViewAll/ViewAllAdvertisers)
        /// </summary>
        public static bool IsRestrictedField(string fieldName)
        {
            return RestrictedFields.Contains(fieldName);
        }

        /// <summary>
        /// Lấy danh sách fields được phép hiển thị theo quyền
        /// </summary>
        public static HashSet<string> GetAllowedFields(bool hasViewSpending, bool hasViewMetrics, bool hasViewAll, bool hasViewAllAdvertisers = false)
        {
            var allowedFields = new HashSet<string>(BasicFields);

            if (hasViewAll || hasViewAllAdvertisers)
            {
                // ViewAll hoặc ViewAllAdvertisers bao gồm tất cả fields
                allowedFields.UnionWith(SpendingFields);
                allowedFields.UnionWith(MetricsFields);
                allowedFields.UnionWith(RestrictedFields);
            }
            else
            {
                // Chỉ thêm fields theo quyền cụ thể
                if (hasViewSpending)
                {
                    allowedFields.UnionWith(SpendingFields);
                }

                if (hasViewMetrics)
                {
                    allowedFields.UnionWith(MetricsFields);
                }
                
                // RestrictedFields chỉ được thêm khi có ViewAll hoặc ViewAllAdvertisers
                // (không thêm ở đây)
            }

            return allowedFields;
        }

        /// <summary>
        /// Kiểm tra xem field có được phép hiển thị không
        /// </summary>
        public static bool IsFieldAllowed(string fieldName, HashSet<string> allowedFields)
        {
            return BasicFields.Contains(fieldName) || allowedFields.Contains(fieldName);
        }
    }
}
