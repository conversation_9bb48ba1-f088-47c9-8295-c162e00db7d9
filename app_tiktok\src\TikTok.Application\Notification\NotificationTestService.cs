using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.Application.Contracts.Notification;
using TikTok.Application.Notification;
using TikTok.Consts;
using Volo.Abp;

namespace TikTok.Application.Notification
{
    /// <summary>
    /// Service chung để test tất cả loại notification system
    /// Hiện tại hỗ trợ GMV Max Creative Status Change
    /// Có thể mở rộng cho các loại notification khác trong tương lai
    /// </summary>
    public class NotificationTestService : INotificationTestService
    {
        private readonly IRawGmvMaxProductCreativeReportRepository _gmvMaxProductCreativeReportRepository;
        private readonly IAssetRepository _assetRepository;
        private readonly IRawGmvMaxCampaignsRepository _gmvMaxCampaignsRepository;
        private readonly ITikTokNotificationService _tikTokNotificationService;
        private readonly CreativeStatusChangeTracker _statusChangeTracker;
        private readonly ILogger<NotificationTestService> _logger;

        public NotificationTestService(
            IRawGmvMaxProductCreativeReportRepository gmvMaxProductCreativeReportRepository,
            ILogger<NotificationTestService> logger,
            IAssetRepository assetRepository,
            IRawGmvMaxCampaignsRepository gmvMaxCampaignsRepository,
            ITikTokNotificationService tikTokNotificationService,
            CreativeStatusChangeTracker statusChangeTracker)
        {
            _gmvMaxProductCreativeReportRepository = gmvMaxProductCreativeReportRepository;
            _assetRepository = assetRepository;
            _gmvMaxCampaignsRepository = gmvMaxCampaignsRepository;
            _tikTokNotificationService = tikTokNotificationService;
            _statusChangeTracker = statusChangeTracker;
            _logger = logger;
        }

        /// <summary>
        /// Test notification với data thật nhưng giới hạn phạm vi
        /// </summary>
        public async Task<NotificationTestResult> TestNotificationAsync(
            string campaignId, 
            string notificationType = "GmvMaxCreativeStatusChange",
            int maxDays = 1)
        {
            var result = new NotificationTestResult
            {
                NotificationType = notificationType,
                CampaignId = campaignId,
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogInformation("=== BẮT ĐẦU TEST NOTIFICATION ===");
                _logger.LogInformation("Type: {NotificationType}, Campaign: {CampaignId}, MaxDays: {MaxDays}", 
                    notificationType, campaignId, maxDays);

                // Hiện tại chỉ hỗ trợ GMV Max Creative Status Change
                // Có thể mở rộng cho các loại notification khác
                switch (notificationType.ToLower())
                {
                    case "gmvmaxcreativestatuschange":
                    case "gmvmax":
                        await TestGmvMaxCreativeStatusChangeAsync(campaignId, maxDays, result);
                        break;
                    default:
                        throw new BusinessException($"Notification type '{notificationType}' is not supported yet");
                }

                result.Success = true;
                _logger.LogInformation("=== HOÀN THÀNH TEST NOTIFICATION ===");
            }
            catch (BusinessException ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.ErrorCode = ex.Code ?? string.Empty;
                _logger.LogError(ex, "Lỗi khi Test Notification cho Campaign: {CampaignId}, Type: {NotificationType}", campaignId, notificationType);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"Lỗi khi Test Notification: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi Test Notification cho Campaign: {CampaignId}, Type: {NotificationType}", campaignId, notificationType);
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
            }

            return result;
        }

        /// <summary>
        /// Test GMV Max Creative Status Change notification - CÁCH ĐƠN GIẢN
        /// </summary>
        private async Task TestGmvMaxCreativeStatusChangeAsync(
            string campaignId, 
            int maxDays, 
            NotificationTestResult result)
        {
            _logger.LogInformation("TEST: Bắt đầu test notification - CÁCH ĐƠN GIẢN");

            try
            {
                // 1. Kiểm tra campaign có tồn tại không
                var campaign = await _gmvMaxCampaignsRepository.FindAsync(c => c.CampaignId == campaignId);
                if (campaign == null)
                {
                    _logger.LogWarning("TEST: Campaign không tồn tại: {CampaignId}", campaignId);
                    result.ErrorMessage = $"Campaign {campaignId} không tồn tại";
                    return;
                }

                _logger.LogInformation("TEST: Tìm thấy campaign: {CampaignId} - {CampaignName}", campaign.CampaignId, campaign.CampaignName);

                // 2. Lấy tất cả product creative của campaign này trong maxDays ngày gần nhất
                var endDate = DateTime.UtcNow;
                var startDate = endDate.AddDays(-maxDays);
                
                var existingRecords = await _gmvMaxProductCreativeReportRepository.GetListAsync(
                    x => x.CampaignId == campaignId && 
                         x.Date >= startDate && 
                         x.Date <= endDate);

                if (!existingRecords.Any())
                {
                    _logger.LogWarning("TEST: Không tìm thấy product creative nào cho campaign {CampaignId} trong {MaxDays} ngày gần nhất", 
                        campaignId, maxDays);
                    result.ErrorMessage = $"Không tìm thấy product creative nào cho campaign {campaignId} trong {maxDays} ngày gần nhất";
                    return;
                }

                _logger.LogInformation("TEST: Tìm thấy {Count} product creative để test", existingRecords.Count);

                // 3. Đổi trạng thái tất cả sang problematic (UPDATE IN-PLACE, không đổi Date)
                var problematicStatuses = new[] 
                { 
                    CreativeDeliveryStatus.NOT_DELIVERYIN, 
                    CreativeDeliveryStatus.EXCLUDED, 
                    CreativeDeliveryStatus.UNAVAILABLE, 
                    CreativeDeliveryStatus.REJECTED 
                };

                var updatedRecords = new List<RawGmvMaxProductCreativeReportEntity>();
                var statusChanges = new List<CreativeStatusChangeInfo>();

                foreach (var existingRecord in existingRecords)
                {
                    var newStatus = problematicStatuses[new Random().Next(problematicStatuses.Length)];
                    var oldStatus = existingRecord.CreativeDeliveryStatus;

                    if (oldStatus == newStatus)
                    {
                        // Nếu trùng ngẫu nhiên, bỏ qua để đảm bảo có thay đổi
                        continue;
                    }

                    _logger.LogInformation("TEST: Đổi trạng thái {CreativeId} từ {OldStatus} sang {NewStatus}", 
                        existingRecord.ItemId, oldStatus, newStatus);

                    existingRecord.CreativeDeliveryStatus = newStatus; // giữ nguyên Date
                    updatedRecords.Add(existingRecord);

                    // Track status change
                    statusChanges.Add(new CreativeStatusChangeInfo
                    {
                        CampaignId = existingRecord.CampaignId,
                        CreativeId = existingRecord.ItemId,
                        OldStatus = oldStatus,
                        NewStatus = newStatus,
                        AdvertiserId = existingRecord.AdvertiserId,
                        BcId = existingRecord.BcId,
                        ChangeTime = DateTime.UtcNow,
                        CampaignName = campaign.CampaignName
                    });
                }

                if (updatedRecords.Any())
                {
                    _logger.LogInformation("TEST: Updating {Count} records in-place", updatedRecords.Count);
                    await _gmvMaxProductCreativeReportRepository.UpdateManyAsync(updatedRecords);
                }
                else
                {
                    _logger.LogWarning("TEST: Không có bản ghi nào cần cập nhật trạng thái");
                }

                // 5. Track tất cả status changes
                foreach (var statusChange in statusChanges)
                {
                    _statusChangeTracker.TrackStatusChange(statusChange);
                }

                _logger.LogInformation("TEST: Đã track {Count} status changes", statusChanges.Count);

                // 6. Gửi notification
                await ProcessStatusChangeNotificationsAsync(result);

                result.TotalRecords = existingRecords.Count;
                result.UpdatedRecords = updatedRecords.Count;
                result.CampaignsProcessed = 1;
                result.CreativesProcessed = existingRecords.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TEST: Lỗi khi test notification");
                throw;
            }

            _logger.LogInformation("TEST: Kết quả - Tổng: {Total}, Updated: {Updated}, Notifications: {Notifications}", 
                result.TotalRecords, result.UpdatedRecords, result.NotificationsSent);
        }



        private async Task<string> GetCampaignNameAsync(string campaignId)
        {
            try
            {
                var campaign = await _gmvMaxCampaignsRepository.FindAsync(c => c.CampaignId == campaignId);
                return campaign?.CampaignName ?? $"Campaign {campaignId}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TEST: Error getting campaign name for {CampaignId}", campaignId);
                return $"Campaign {campaignId}";
            }
        }

        /// <summary>
        /// Kiểm tra xem trạng thái có phải là problematic không
        /// </summary>
        private bool IsProblematicStatus(CreativeDeliveryStatus? status)
        {
            if (!status.HasValue)
                return false;

            var problematicStatuses = new[]
            {
                CreativeDeliveryStatus.NOT_DELIVERYIN,
                CreativeDeliveryStatus.EXCLUDED,
                CreativeDeliveryStatus.UNAVAILABLE,
                CreativeDeliveryStatus.REJECTED
            };

            return problematicStatuses.Contains(status.Value);
        }

        /// <summary>
        /// Xử lý thông báo cho các thay đổi trạng thái creative
        /// </summary>
        private async Task ProcessStatusChangeNotificationsAsync(NotificationTestResult result)
        {
            try
            {
                var campaignSummaries = _statusChangeTracker.GetCampaignSummaries();
                
                if (!campaignSummaries.Any())
                {
                    _logger.LogInformation("TEST: No campaign summaries to process for notifications");
                    return;
                }

                _logger.LogInformation("TEST: Processing notifications for {Count} campaigns with status changes", campaignSummaries.Count);

                foreach (var summary in campaignSummaries)
                {
                    if (summary.HasProblematicStatuses)
                    {
                        _logger.LogInformation("TEST: Sending notification for campaign {CampaignId} with {ProblematicCount} problematic creatives",
                            summary.CampaignId, summary.ProblematicCreativesCount);
                        
                        // KIỂM TRA: CampaignId trước khi gửi notification
                        _logger.LogInformation("TEST: CampaignId being sent to notification: {CampaignId}", summary.CampaignId);

                        var success = await _tikTokNotificationService.SendCampaignNotificationAsync(
                            summary.CampaignId,
                            TikTokNotificationConst.GmvMaxCreativeStatusChange,
                            new Dictionary<string, object>
                            {
                                ["StatusSummary"] = summary,
                                ["ChangeTime"] = summary.LastChangeTime,
                                ["ProblematicCount"] = summary.ProblematicCreativesCount
                            });

                        if (success)
                        {
                            result.NotificationsSent++;
                            result.CampaignsWithNotifications.Add(summary.CampaignId);
                            _logger.LogInformation("TEST: Successfully sent notification for campaign {CampaignId}", summary.CampaignId);
                        }
                        else
                        {
                            _logger.LogWarning("TEST: Failed to send notification for campaign {CampaignId}", summary.CampaignId);
                        }
                    }
                    else
                    {
                        _logger.LogInformation("TEST: Skipping notification for campaign {CampaignId} - no problematic statuses", summary.CampaignId);
                    }
                }

                _statusChangeTracker.Clear();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TEST: Error processing status change notifications");
            }
        }
    }
}
