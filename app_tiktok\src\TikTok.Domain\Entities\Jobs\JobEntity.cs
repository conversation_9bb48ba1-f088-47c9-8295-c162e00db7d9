using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Entities
{
    /// <summary>
    /// Entity quản lý công việc đồng bộ
    /// </summary>
    public class JobEntity : AuditedEntity<Guid>
    {
        /// <summary>
        /// Loại lệnh
        /// </summary>
        [Required]
        public CommandType CommandType { get; set; }

        /// <summary>
        /// Trạng thái công việc
        /// </summary>
        [Required]
        public JobStatus Status { get; set; }

        /// <summary>
        /// ID của Business Application liên quan
        /// </summary>
        public Guid? BusinessApplicationId { get; set; }

        /// <summary>
        /// Tham số JSON cho công việc
        /// </summary>
        public string? Parameters { get; set; }

        /// <summary>
        /// Thông báo lỗi nếu có
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Thời gian bắt đầu xử lý
        /// </summary>
        public DateTime? StartedAt { get; set; }

        /// <summary>
        /// Thời gian hoàn thành
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// Thời gian hết hạn của job (mặc định 7 ngày từ khi tạo)
        /// </summary>
        [Required]
        public DateTime ExpireAt { get; set; }

        /// <summary>
        /// ID của worker đang xử lý
        /// </summary>
        public string? WorkerId { get; set; }

        /// <summary>
        /// Kết quả JSON của công việc
        /// </summary>
        public string? Result { get; set; }

        /// <summary>
        /// Số lần thử lại
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// Số lần thử lại tối đa
        /// </summary>
        public int MaxRetryCount { get; set; }

        /// <summary>
        /// Độ ưu tiên (số càng cao càng ưu tiên)
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string? Notes { get; set; }

        public JobEntity()
        {
            Status = JobStatus.Pending;
            RetryCount = 0;
            MaxRetryCount = 0;
            Priority = 1;
            // Mặc định hết hạn sau 7 ngày từ khi tạo
            ExpireAt = DateTime.UtcNow.AddDays(7);
        }

        /// <summary>
        /// Bắt đầu xử lý công việc
        /// </summary>
        /// <param name="workerId">ID của worker</param>
        public void StartProcessing(string workerId)
        {
            Status = JobStatus.InProcess;
            WorkerId = workerId;
            StartedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Hoàn thành công việc
        /// </summary>
        /// <param name="result">Kết quả</param>
        public void Complete(string? result = null)
        {
            Status = JobStatus.Completed;
            Result = result;
            CompletedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Đánh dấu lỗi
        /// </summary>
        /// <param name="errorMessage">Thông báo lỗi</param>
        public void MarkAsError(string errorMessage)
        {
            Status = JobStatus.Error;
            ErrorMessage = errorMessage;
            CompletedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Hủy công việc
        /// </summary>
        public void Cancel()
        {
            Status = JobStatus.Cancelled;
            CompletedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Thử lại công việc
        /// </summary>
        public void Retry()
        {
            if (RetryCount <= MaxRetryCount)
            {
                RetryCount++;
                Status = JobStatus.Pending;
                WorkerId = null;
                StartedAt = null;
                CompletedAt = null;
                ErrorMessage = null;
            }
            else
            {
                // Lỗi quá số lần thử lại
                Status = JobStatus.Error;
                ErrorMessage = "Lỗi quá số lần thử lại";
                CompletedAt = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Kiểm tra xem job có hết hạn chưa
        /// </summary>
        /// <returns>True nếu job đã hết hạn</returns>
        public bool IsExpired()
        {
            return DateTime.UtcNow > ExpireAt;
        }

        /// <summary>
        /// Thiết lập thời gian hết hạn
        /// </summary>
        /// <param name="expireAt">Thời gian hết hạn</param>
        public void SetExpiration(DateTime expireAt)
        {
            ExpireAt = expireAt;
        }

        /// <summary>
        /// Thiết lập thời gian hết hạn theo số ngày
        /// </summary>
        /// <param name="days">Số ngày từ hiện tại</param>
        public void SetExpirationInDays(int days)
        {
            ExpireAt = DateTime.UtcNow.AddDays(days);
        }

        public void SetCreateTime(DateTime createTime)
        {
            CreationTime = createTime;
            // Cập nhật thời gian hết hạn dựa trên thời gian tạo
            ExpireAt = createTime.AddDays(7); // Mặc định 7 ngày từ khi tạo
        }
    }
}