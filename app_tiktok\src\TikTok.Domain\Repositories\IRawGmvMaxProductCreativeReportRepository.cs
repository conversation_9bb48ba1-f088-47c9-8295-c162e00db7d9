using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTok.Entities;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Repositories
{
    /// <summary>
    /// Repository interface cho GMV Max Product Creative Reports
    /// </summary>
    public interface IRawGmvMaxProductCreativeReportRepository : IRepository<RawGmvMaxProductCreativeReportEntity, Guid>
    {
        /// <summary>
        /// Lấy danh sách Creative Reports theo Campaign IDs
        /// </summary>
        /// <param name="campaignIds">Danh sách Campaign IDs</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách Creative Reports</returns>
        Task<List<RawGmvMaxProductCreativeReportEntity>> GetByCampaignIdsAsync(
            List<string> campaignIds,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// L<PERSON>y danh sách Creative Reports theo Item Group IDs
        /// </summary>
        /// <param name="itemGroupIds">Danh sách Item Group IDs</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách Creative Reports</returns>
        Task<List<RawGmvMaxProductCreativeReportEntity>> GetByItemGroupIdsAsync(
            List<string> itemGroupIds,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách Creative Reports theo Item IDs
        /// </summary>
        /// <param name="itemIds">Danh sách Item IDs</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách Creative Reports</returns>
        Task<List<RawGmvMaxProductCreativeReportEntity>> GetByItemIdsAsync(
            List<string> itemIds,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách Creative Reports theo Campaign IDs trong khoảng thời gian
        /// </summary>
        /// <param name="campaignIds">Danh sách Campaign IDs</param>
        /// <param name="fromDate">Từ ngày (Date)</param>
        /// <param name="toDate">Đến ngày (Date)</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách Creative Reports trong khoảng thời gian</returns>
        Task<List<RawGmvMaxProductCreativeReportEntity>> GetByCampaignIdsAndDateRangeAsync(
            List<string> campaignIds,
            DateTime fromDate,
            DateTime toDate,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách Creative Reports theo Item Group IDs trong khoảng thời gian
        /// </summary>
        /// <param name="itemGroupIds">Danh sách Item Group IDs</param>
        /// <param name="fromDate">Từ ngày (Date)</param>
        /// <param name="toDate">Đến ngày (Date)</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách Creative Reports trong khoảng thời gian</returns>
        Task<List<RawGmvMaxProductCreativeReportEntity>> GetByItemGroupIdsAndDateRangeAsync(
            List<string> itemGroupIds,
            DateTime fromDate,
            DateTime toDate,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách Creative Reports theo Store ID
        /// </summary>
        /// <param name="storeId">Store ID</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách Creative Reports</returns>
        Task<List<RawGmvMaxProductCreativeReportEntity>> GetByStoreIdAsync(
            string storeId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách Creative Reports theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách Creative Reports</returns>
        Task<List<RawGmvMaxProductCreativeReportEntity>> GetByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy báo cáo mới nhất theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Báo cáo mới nhất hoặc null nếu không tìm thấy</returns>
        Task<RawGmvMaxProductCreativeReportEntity?> GetLatestByBcIdAsync(
            string bcId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy báo cáo mới nhất theo BC ID và Advertiser ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Báo cáo mới nhất hoặc null nếu không tìm thấy</returns>
        Task<RawGmvMaxProductCreativeReportEntity?> GetLatestByBcIdAndAdvertiserIdAsync(
            string bcId,
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách báo cáo mới nhất theo campaignIds, advertiserIds, itemGroupIds, itemIds
        /// </summary>
        /// <param name="campaignIds">Danh sách Campaign IDs</param>
        /// <param name="advertiserIds">Danh sách Advertiser IDs</param>
        /// <param name="itemGroupIds">Danh sách Item Group IDs</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách báo cáo mới nhất</returns>
        Task<List<RawGmvMaxProductCreativeReportEntity>> GetLatestByCampaignIdsAndAdvertiserIdsAndItemGroupIdsAsync(
            List<string> campaignIds,
            List<string> advertiserIds,
            List<string> itemGroupIds,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Cập nhật hàng loạt các bản ghi Creative Reports
        /// </summary>
        /// <param name="entities">Danh sách entities cần cập nhật</param>
        /// <param name="cancellationToken">Token hủy</param>
        Task UpdateManyAsync(IEnumerable<RawGmvMaxProductCreativeReportEntity> entities, CancellationToken cancellationToken = default);
    }
} 