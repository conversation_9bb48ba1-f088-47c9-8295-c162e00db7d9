using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.RawGmvMaxProductCreativeReports;
using TikTok.Repositories;
using Volo.Abp.EntityFrameworkCore;

namespace TikTok.EntityFrameworkCore.Repositories
{
    public class RawGmvMaxProductCreativeReportDapperRepository : DapperRepository<RawGmvMaxProductCreativeReportEntity, Guid>, IRawGmvMaxProductCreativeReportDapperRepository
    {
        private const string TableName = "[dbo].[Raw_RawGmvMaxProductCreativeReports]";

        public RawGmvMaxProductCreativeReportDapperRepository(IDbContextProvider<TikTokDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<RawGmvMaxProductCreativeReportEntity?> GetAsync(Guid id)
        {
            var sql = $@"SELECT TOP 1 * FROM {TableName} WHERE Id = @Id";
            return await QuerySingleOrDefaultAsync<RawGmvMaxProductCreativeReportEntity>(sql, new { Id = id });
        }

        public async Task<List<string>> GetCampaignIdsAsync()
        {
            var sql = $@"SELECT DISTINCT CampaignId FROM {TableName} WHERE CampaignId IS NOT NULL AND CampaignId <> '' ORDER BY CampaignId";
            var result = await QueryAsync<string>(sql);
            return result.ToList();
        }

        public async Task<long> GetCountAsync(
            string? searchText = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? campaignId = null,
            List<string>? campaignIds = null,
            List<ShopContentType>? shopContentTypes = null,
            List<CreativeDeliveryStatus>? creativeDeliveryStatuses = null)
        {
            var (whereSql, parameters) = BuildWhereClause(searchText, fromDate, toDate, campaignId, campaignIds, shopContentTypes, creativeDeliveryStatuses);
            var sql = $@"SELECT COUNT(1) FROM {TableName} {whereSql}";
            var count = await ExecuteScalarAsync<long>(sql, parameters);
            return count;
        }

        public async Task<List<RawGmvMaxProductCreativeReportEntity>> GetListAsync(
            string? searchText = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? campaignId = null,
            List<string>? campaignIds = null,
            List<ShopContentType>? shopContentTypes = null,
            List<CreativeDeliveryStatus>? creativeDeliveryStatuses = null,
            int skipCount = 0,
            int maxResultCount = 20,
            string? sorting = null)
        {
            var (whereSql, parameters) = BuildWhereClause(searchText, fromDate, toDate, campaignId, campaignIds, shopContentTypes, creativeDeliveryStatuses);

            var orderBy = BuildOrderByClause(sorting);

            var sql = new StringBuilder();
            sql.Append($"SELECT * FROM {TableName} ");
            sql.Append(whereSql);
            sql.Append(' ');
            sql.Append(orderBy);
            sql.Append(" OFFSET @Skip ROWS FETCH NEXT @Take ROWS ONLY");

            var paramWithPaging = MergeParameters(parameters, new { Skip = skipCount, Take = maxResultCount });

            var items = await QueryAsync<RawGmvMaxProductCreativeReportEntity>(sql.ToString(), paramWithPaging);
            return items.ToList();
        }

        private static (string whereSql, object parameters) BuildWhereClause(
            string? searchText,
            DateTime? fromDate,
            DateTime? toDate,
            string? campaignId,
            List<string>? campaignIds,
            List<ShopContentType>? shopContentTypes,
            List<CreativeDeliveryStatus>? creativeDeliveryStatuses)
        {
            var conditions = new List<string>();
            var param = new Dictionary<string, object?>();

            if (!string.IsNullOrWhiteSpace(searchText))
            {
                conditions.Add("(Title LIKE @Search OR CampaignId LIKE @Search OR TtAccountName LIKE @Search OR ItemGroupId LIKE @Search OR ItemId LIKE @Search)");
                param["Search"] = "%" + searchText.Trim() + "%";
            }

            if (fromDate.HasValue)
            {
                conditions.Add("[Date] >= @FromDate");
                param["FromDate"] = fromDate.Value;
            }
            if (toDate.HasValue)
            {
                conditions.Add("[Date] <= @ToDate");
                param["ToDate"] = toDate.Value;
            }

            if (!string.IsNullOrWhiteSpace(campaignId))
            {
                conditions.Add("CampaignId = @CampaignId");
                param["CampaignId"] = campaignId;
            }

            if (campaignIds != null && campaignIds.Count > 0)
            {
                var inParams = new List<string>();
                for (int i = 0; i < campaignIds.Count; i++)
                {
                    var key = $"CampaignId_{i}";
                    inParams.Add($"@{key}");
                    param[key] = campaignIds[i];
                }
                conditions.Add($"CampaignId IN ({string.Join(",", inParams)})");
            }

            if (shopContentTypes != null && shopContentTypes.Count > 0)
            {
                var values = string.Join(",", shopContentTypes.Select(v => ((int)v).ToString()));
                conditions.Add($"ShopContentType IN ({values})");
            }

            if (creativeDeliveryStatuses != null && creativeDeliveryStatuses.Count > 0)
            {
                var values = string.Join(",", creativeDeliveryStatuses.Select(v => ((int)v).ToString()));
                conditions.Add($"CreativeDeliveryStatus IN ({values})");
            }

            var where = conditions.Count > 0 ? ("WHERE " + string.Join(" AND ", conditions)) : string.Empty;
            return (where, param);
        }

        private static string BuildOrderByClause(string? sorting)
        {
            // Whitelist các cột cho phép sắp xếp
            var allowed = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "CampaignId","Title","ShopContentType","CreativeDeliveryStatus","Orders","GrossRevenue","Cost","ROI","Date"
            };

            string column = "Date";
            string direction = "DESC";

            if (!string.IsNullOrWhiteSpace(sorting))
            {
                var parts = sorting.Trim().Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 1 && allowed.Contains(parts[0]))
                {
                    column = parts[0];
                }
                if (parts.Length >= 2)
                {
                    var dir = parts[1].ToUpperInvariant();
                    if (dir == "ASC" || dir == "DESC")
                    {
                        direction = dir;
                    }
                }
            }

            return $"ORDER BY {column} {direction}";
        }

        private static object MergeParameters(object baseParams, object extra)
        {
            var dict = new Dictionary<string, object?>();
            if (baseParams is Dictionary<string, object?> d1)
            {
                foreach (var kv in d1) dict[kv.Key] = kv.Value!;
            }
            else if (baseParams != null)
            {
                foreach (var p in baseParams.GetType().GetProperties())
                {
                    dict[p.Name] = p.GetValue(baseParams);
                }
            }
            foreach (var p in extra.GetType().GetProperties())
            {
                dict[p.Name] = p.GetValue(extra)!;
            }
            return dict;
        }

        /// <summary>
        /// Lấy thống kê video theo trạng thái
        /// </summary>
        public async Task<List<VideoStatusCountDto>> GetVideoStatusStatisticsAsync(
            string? searchText = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            List<string>? campaignIds = null,
            List<ShopContentType>? shopContentTypes = null)
        {
            var (whereSql, parameters) = BuildWhereClause(
                searchText, fromDate, toDate, null, campaignIds, shopContentTypes, null);

            var sql = $@"
                SELECT 
                    CreativeDeliveryStatus as Status,
                    COUNT(1) as Count
                FROM {TableName} 
                {whereSql}
                GROUP BY CreativeDeliveryStatus
                ORDER BY CreativeDeliveryStatus";

            var result = await QueryAsync<VideoStatusCountDto>(sql, parameters);
            return result.ToList();
        }

        /// <summary>
        /// Lấy báo cáo mới nhất theo CampaignIds, AdvertiserIds và ItemGroupIds
        /// Sử dụng CTE với ROW_NUMBER() để tối ưu performance
        /// </summary>
        public async Task<List<RawGmvMaxProductCreativeReportEntity>> GetLatestByCampaignIdsAndAdvertiserIdsAndItemGroupIdsAsync(
            List<string> campaignIds,
            List<string> advertiserIds,
            List<string> itemGroupIds)
        {
            if (!campaignIds.Any() || !advertiserIds.Any() || !itemGroupIds.Any())
                return new List<RawGmvMaxProductCreativeReportEntity>();

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Build parameters để tránh SQL injection
            var parameters = new Dictionary<string, object>();
            var campaignParams = new List<string>();
            var advertiserParams = new List<string>();
            var itemGroupParams = new List<string>();

            // Build campaign parameters
            for (int i = 0; i < campaignIds.Count; i++)
            {
                var paramName = $"Campaign_{i}";
                campaignParams.Add($"@{paramName}");
                parameters[paramName] = campaignIds[i];
            }

            // Build advertiser parameters  
            for (int i = 0; i < advertiserIds.Count; i++)
            {
                var paramName = $"Advertiser_{i}";
                advertiserParams.Add($"@{paramName}");
                parameters[paramName] = advertiserIds[i];
            }

            // Build item group parameters
            for (int i = 0; i < itemGroupIds.Count; i++)
            {
                var paramName = $"ItemGroup_{i}";
                itemGroupParams.Add($"@{paramName}");
                parameters[paramName] = itemGroupIds[i];
            }

            var sql = $@"
                WITH RankedReports AS (
                    SELECT *, 
                           ROW_NUMBER() OVER (PARTITION BY ItemId ORDER BY Date DESC) as rn
                    FROM {TableName} 
                    WHERE CampaignId IN ({string.Join(",", campaignParams)}) 
                      AND AdvertiserId IN ({string.Join(",", advertiserParams)}) 
                      AND ItemGroupId IN ({string.Join(",", itemGroupParams)})
                )
                SELECT * FROM RankedReports WHERE rn = 1";

            var result = await QueryAsync<RawGmvMaxProductCreativeReportEntity>(sql, parameters);
            stopwatch.Stop();

            // Log performance metrics
            var resultList = result.ToList();
            if (stopwatch.ElapsedMilliseconds > 5000) // Log if > 5 seconds
            {
                Console.WriteLine($"[PERF WARNING] Dapper GetLatestByCampaignIds took {stopwatch.ElapsedMilliseconds}ms for {campaignIds.Count}C/{advertiserIds.Count}A/{itemGroupIds.Count}IG -> {resultList.Count} records");
            }
            else
            {
                Console.WriteLine($"[PERF INFO] Dapper GetLatestByCampaignIds took {stopwatch.ElapsedMilliseconds}ms for {campaignIds.Count}C/{advertiserIds.Count}A/{itemGroupIds.Count}IG -> {resultList.Count} records");
            }

            return resultList;
        }
    }
}


