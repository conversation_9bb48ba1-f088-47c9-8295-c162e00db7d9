using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TikTok.Entities;
using TikTok.EntityFrameworkCore;
using TikTok.Repositories;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TikTok.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// Repository implementation cho GMV Max Product Creative Reports
    /// </summary>
    public class RawGmvMaxProductCreativeReportRepository : EfCoreRepository<TikTokDbContext, RawGmvMaxProductCreativeReportEntity, Guid>, IRawGmvMaxProductCreativeReportRepository
    {
        public RawGmvMaxProductCreativeReportRepository(IDbContextProvider<TikTokDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        /// <summary>
        /// L<PERSON>y danh sách Creative Reports theo Campaign IDs
        /// </summary>
        public async Task<List<RawGmvMaxProductCreativeReportEntity>> GetByCampaignIdsAsync(
            List<string> campaignIds,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => campaignIds.Contains(x.CampaignId))
                .OrderBy(x => x.CampaignId)
                .ThenBy(x => x.ItemGroupId)
                .ThenBy(x => x.ItemId)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// Lấy danh sách Creative Reports theo Item Group IDs
        /// </summary>
        public async Task<List<RawGmvMaxProductCreativeReportEntity>> GetByItemGroupIdsAsync(
            List<string> itemGroupIds,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => itemGroupIds.Contains(x.ItemGroupId))
                .OrderBy(x => x.ItemGroupId)
                .ThenBy(x => x.ItemId)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// Lấy danh sách Creative Reports theo Item IDs
        /// </summary>
        public async Task<List<RawGmvMaxProductCreativeReportEntity>> GetByItemIdsAsync(
            List<string> itemIds,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => itemIds.Contains(x.ItemId))
                .OrderBy(x => x.ItemId)
                .ThenBy(x => x.Date)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// Lấy danh sách Creative Reports theo Campaign IDs trong khoảng thời gian
        /// </summary>
        public async Task<List<RawGmvMaxProductCreativeReportEntity>> GetByCampaignIdsAndDateRangeAsync(
            List<string> campaignIds,
            DateTime fromDate,
            DateTime toDate,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => campaignIds.Contains(x.CampaignId) && 
                           x.Date >= fromDate && 
                           x.Date <= toDate)
                .OrderBy(x => x.CampaignId)
                .ThenBy(x => x.ItemGroupId)
                .ThenBy(x => x.ItemId)
                .ThenBy(x => x.Date)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// Lấy danh sách Creative Reports theo Item Group IDs trong khoảng thời gian
        /// </summary>
        public async Task<List<RawGmvMaxProductCreativeReportEntity>> GetByItemGroupIdsAndDateRangeAsync(
            List<string> itemGroupIds,
            DateTime fromDate,
            DateTime toDate,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => itemGroupIds.Contains(x.ItemGroupId) && 
                           x.Date >= fromDate && 
                           x.Date <= toDate)
                .OrderBy(x => x.ItemGroupId)
                .ThenBy(x => x.ItemId)
                .ThenBy(x => x.Date)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// Lấy danh sách Creative Reports theo Store ID
        /// </summary>
        public async Task<List<RawGmvMaxProductCreativeReportEntity>> GetByStoreIdAsync(
            string storeId,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.StoreId == storeId)
                .OrderBy(x => x.CampaignId)
                .ThenBy(x => x.ItemGroupId)
                .ThenBy(x => x.ItemId)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// Lấy danh sách Creative Reports theo Advertiser ID
        /// </summary>
        public async Task<List<RawGmvMaxProductCreativeReportEntity>> GetByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.AdvertiserId == advertiserId)
                .OrderBy(x => x.CampaignId)
                .ThenBy(x => x.ItemGroupId)
                .ThenBy(x => x.ItemId)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// Lấy báo cáo mới nhất theo BC ID
        /// </summary>
        public async Task<RawGmvMaxProductCreativeReportEntity?> GetLatestByBcIdAsync(
            string bcId,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.BcId == bcId)
                .OrderByDescending(x => x.Date)
                .FirstOrDefaultAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// Lấy báo cáo mới nhất theo BC ID và Advertiser ID
        /// </summary>
        public async Task<RawGmvMaxProductCreativeReportEntity?> GetLatestByBcIdAndAdvertiserIdAsync(
            string bcId,
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.BcId == bcId && x.AdvertiserId == advertiserId)
                .OrderByDescending(x => x.Date)
                .FirstOrDefaultAsync(GetCancellationToken(cancellationToken));
        }

    /// <summary>
    /// Lấy danh sách báo cáo mới nhất theo campaignIds, advertiserIds, itemGroupIds, itemIds
    /// </summary>
    /// <param name="campaignIds">Danh sách Campaign IDs</param>
    /// <param name="advertiserIds">Danh sách Advertiser IDs</param>
    /// <param name="itemGroupIds">Danh sách Item Group IDs</param>
    /// <param name="cancellationToken">Token hủy</param>
    /// <returns>Danh sách báo cáo mới nhất</returns>
        public async Task<List<RawGmvMaxProductCreativeReportEntity>> GetLatestByCampaignIdsAndAdvertiserIdsAndItemGroupIdsAsync(
            List<string> campaignIds,
            List<string> advertiserIds,
            List<string> itemGroupIds,
            CancellationToken cancellationToken = default)
        {

            var dbContext = await GetDbContextAsync();
            return await (from b in dbContext.RawGmvMaxProductCreativeReports
                            where campaignIds.Contains(b.CampaignId) && 
                                  advertiserIds.Contains(b.AdvertiserId) && 
                                  itemGroupIds.Contains(b.ItemGroupId)
                            group b by b.ItemId into g
                            select g.OrderByDescending(x => x.Date).FirstOrDefault())
               .ToListAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// Cập nhật hàng loạt các bản ghi Creative Reports
        /// </summary>
        public async Task UpdateManyAsync(IEnumerable<RawGmvMaxProductCreativeReportEntity> entities, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var dbSet = await GetDbSetAsync();

            foreach (var entity in entities)
            {
                dbSet.Update(entity);
            }

            await dbContext.SaveChangesAsync(GetCancellationToken(cancellationToken));
        }
    }

    /// <summary>
    /// Constants cho RawGmvMaxProductCreativeReportEntity
    /// </summary>
    public static class RawGmvMaxProductCreativeReportEntityConsts
    {
        private const string DefaultSorting = "{0}Date desc";

        public static string GetDefaultSorting(bool withEntityName)
        {
            return string.Format(DefaultSorting, withEntityName ? "RawGmvMaxProductCreativeReportEntity." : string.Empty);
        }
    }
} 