using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;
using TikTok.DimCampaigns;
using TikTok.Permissions;

namespace TikTok.HttpApi.Controllers
{
    /// <summary>
    /// Controller for DimCampaign operations - Reusable across multiple features
    /// </summary>
    [Route("api/dim-campaigns")]
    [Authorize]
    public class DimCampaignController : AbpControllerBase
    {
        private readonly IDimCampaignAppService _dimCampaignAppService;

        public DimCampaignController(IDimCampaignAppService dimCampaignAppService)
        {
            _dimCampaignAppService = dimCampaignAppService;
        }

        /// <summary>
        /// Get all campaigns for dropdowns/multiselects (active campaigns only)
        /// </summary>
        /// <param name="format">Response format: 'full' (default) or 'simple' for UI components</param>
        /// <param name="campaignType">Optional: Filter by campaign type</param>
        /// <returns>List of active campaigns</returns>
        [HttpGet]
        public async Task<ActionResult<object>> GetCampaignsAsync([FromQuery] string format = "full", [FromQuery] string? campaignType = null)
        {
            try
            {
                // Check basic permissions - ViewSpending, ViewMetrics, ViewAll, or ViewAllAdvertisers
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập danh sách campaign");
                }

                List<DimCampaignDto> campaigns;

                if (!string.IsNullOrWhiteSpace(campaignType))
                {
                    campaigns = await _dimCampaignAppService.GetCampaignsByTypeAsync(campaignType.ToUpperInvariant());
                }
                else
                {
                    campaigns = await _dimCampaignAppService.GetActiveCampaignsAsync();
                }

                if (format.Equals("simple", StringComparison.OrdinalIgnoreCase))
                {
                    // Return simplified format for UI components like Syncfusion MultiSelect
                    var simplifiedCampaigns = campaigns.Select(c => new
                    {
                        text = c.CampaignName ?? c.CampaignId,
                        value = c.CampaignId,
                        campaignType = c.CampaignType,
                        objectiveType = c.ObjectiveType
                    }).ToList();
                    return Ok(simplifiedCampaigns);
                }
                
                return Ok(campaigns);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get campaigns by type
        /// </summary>
        /// <param name="campaignType">Campaign type (PRODUCT, LIVE, etc.)</param>
        /// <returns>List of campaigns of specified type</returns>
        [HttpGet("by-type/{campaignType}")]
        public async Task<ActionResult<List<DimCampaignDto>>> GetCampaignsByTypeAsync(string campaignType)
        {
            try
            {
                // Check basic permissions
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập danh sách campaign theo loại");
                }

                if (string.IsNullOrWhiteSpace(campaignType))
                {
                    return BadRequest("Campaign type is required");
                }

                var campaigns = await _dimCampaignAppService.GetCampaignsByTypeAsync(campaignType.ToUpperInvariant());
                return Ok(campaigns);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get campaigns by advertiser
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>List of campaigns for specified advertiser</returns>
        [HttpGet("by-advertiser/{advertiserId}")]
        public async Task<ActionResult<List<DimCampaignDto>>> GetCampaignsByAdvertiserAsync(string advertiserId)
        {
            try
            {
                // Check permissions - requires ViewAll or ViewAllAdvertisers for advertiser-specific data
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập danh sách campaign theo advertiser");
                }

                if (string.IsNullOrWhiteSpace(advertiserId))
                {
                    return BadRequest("Advertiser ID is required");
                }

                var campaigns = await _dimCampaignAppService.GetCampaignsByAdvertiserAsync(advertiserId);
                return Ok(campaigns);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }


        /// <summary>
        /// Get campaign types for dropdown
        /// </summary>
        /// <returns>List of available campaign types</returns>
        [HttpGet("types")]
        public async Task<ActionResult<List<object>>> GetCampaignTypesAsync()
        {
            try
            {
                // Check basic permissions
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập danh sách loại campaign");
                }

                // Get all active campaigns and extract unique types
                var campaigns = await _dimCampaignAppService.GetActiveCampaignsAsync();
                var campaignTypes = campaigns
                    .Where(c => !string.IsNullOrWhiteSpace(c.CampaignType))
                    .GroupBy(c => c.CampaignType)
                    .Select(g => new
                    {
                        text = g.Key,
                        value = g.Key,
                        count = g.Count()
                    })
                    .OrderBy(t => t.text)
                    .ToList();

                return Ok(campaignTypes);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
