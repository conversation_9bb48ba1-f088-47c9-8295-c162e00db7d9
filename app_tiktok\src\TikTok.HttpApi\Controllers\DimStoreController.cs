using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;
using TikTok.DimStores;
using TikTok.Permissions;

namespace TikTok.HttpApi.Controllers
{
    /// <summary>
    /// Controller for DimStore operations - Reusable across multiple features
    /// </summary>
    [Route("api/dim-stores")]
    [Authorize]
    public class DimStoreController : AbpControllerBase
    {
        private readonly IDimStoreAppService _dimStoreAppService;

        public DimStoreController(IDimStoreAppService dimStoreAppService)
        {
            _dimStoreAppService = dimStoreAppService;
        }

        /// <summary>
        /// Get all stores for dropdowns/multiselects (active stores only)
        /// </summary>
        /// <param name="format">Response format: 'full' (default) or 'simple' for UI components</param>
        /// <returns>List of active stores</returns>
        [HttpGet]
        public async Task<ActionResult<object>> GetStoresAsync([FromQuery] string format = "full")
        {
            try
            {
                // Check basic permissions - ViewSpending, ViewMetrics, ViewAll, or ViewAllAdvertisers
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập danh sách shop");
                }

                var stores = await _dimStoreAppService.GetActiveStoresAsync();
                
                if (format.Equals("simple", StringComparison.OrdinalIgnoreCase))
                {
                    // Return simplified format for UI components like Syncfusion MultiSelect
                    var simplifiedStores = stores.Select(s => new
                    {
                        text = s.StoreName ?? s.StoreId,
                        value = s.StoreId,
                        storeType = s.StoreType,
                        country = s.Country
                    }).ToList();
                    return Ok(simplifiedStores);
                }
                
                return Ok(stores);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Get stores by country
        /// </summary>
        /// <param name="country">Country code (VN, US, etc.)</param>
        /// <returns>List of stores in specified country</returns>
        [HttpGet("by-country/{country}")]
        public async Task<ActionResult<List<DimStoreDto>>> GetStoresByCountryAsync(string country)
        {
            try
            {
                // Check basic permissions
                var hasViewSpending = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await AuthorizationService.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    return Forbid("Bạn không có quyền truy cập danh sách shop theo quốc gia");
                }

                if (string.IsNullOrWhiteSpace(country))
                {
                    return BadRequest("Country code is required");
                }

                var stores = await _dimStoreAppService.GetStoresByCountryAsync(country.ToUpperInvariant());
                return Ok(stores);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

    }
}
