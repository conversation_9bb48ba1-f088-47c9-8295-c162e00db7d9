using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace TikTok.Web.Middleware;

/// <summary>
/// Middleware to handle automatic login redirects with return URL preservation
/// </summary>
public class ReturnUrlMiddleware
{
    private readonly RequestDelegate _next;

    public ReturnUrlMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Check if user is authenticated and if the endpoint requires authorization
        if (context.User.Identity?.IsAuthenticated != true &&
            context.Request.Method.Equals("GET", StringComparison.OrdinalIgnoreCase))
        {
            // Check if the current endpoint requires authorization
            var endpoint = context.GetEndpoint();
            if (endpoint != null)
            {
                var authorizeData = endpoint.Metadata.GetOrderedMetadata<IAuthorizeData>();
                var allowAnonymous = endpoint.Metadata.GetMetadata<IAllowAnonymous>();

                // If endpoint has [Authorize] and no [AllowAnonymous], redirect to login
                if (authorizeData.Any() && allowAnonymous == null)
                {
                    var returnUrl = context.Request.Path + context.Request.QueryString;
                    var loginUrl = $"/Account/Login?returnUrl={Uri.EscapeDataString(returnUrl)}";
                    context.Response.Redirect(loginUrl);
                    return;
                }
            }
        }

        await _next(context);
    }
}
