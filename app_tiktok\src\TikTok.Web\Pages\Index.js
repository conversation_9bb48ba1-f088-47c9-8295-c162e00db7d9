$(function () {
    abp.log.debug('Index.js initialized!');

    // Check if user is authenticated before loading dashboard
    if (isUserAuthenticated()) {
        // Load dashboard statistics when page loads
        loadDashboardStatistics();

        // Auto refresh every 30 seconds
        setInterval(loadDashboardStatistics, 30000);

        // Setup quick action buttons based on permissions
        setupQuickActionButtons();

        // Display current user information
        displayCurrentUserInfo();
    }
});

/**
 * Check if user is authenticated using abp.currentUser
 */
function isUserAuthenticated() {
    return abp.currentUser && abp.currentUser.isAuthenticated;
}

/**
 * Check authentication and redirect to login if not authenticated
 * @param {string} returnUrl - Optional return URL, defaults to current page
 * @returns {boolean} - True if authenticated, false if redirected
 */
function checkAuthenticationAndRedirect(returnUrl) {
    if (!isUserAuthenticated()) {
        if (!returnUrl) {
            returnUrl = window.location.pathname + window.location.search;
        }
        const encodedReturnUrl = encodeURIComponent(returnUrl);
        window.location.href = `/Account/Login?returnUrl=${encodedReturnUrl}`;
        return false;
    }
    return true;
}

/**
 * Enhanced authentication check with callback support
 * @param {function} callback - Function to execute if authenticated
 * @param {string} returnUrl - Optional return URL
 */
function requireAuthentication(callback, returnUrl) {
    if (checkAuthenticationAndRedirect(returnUrl)) {
        if (typeof callback === 'function') {
            callback();
        }
    }
}

/**
 * Display current user information
 */
function displayCurrentUserInfo() {
    const user = abp.currentUser;
    if (user && user.isAuthenticated) {
        // Update user info in the dashboard
        let userName = user.userName;
        if (user.name) {
            userName = user.name;
            if (user.surName) {
                userName = `${user.surName} ${user.name}`;
            }
        }

        const userEmail = user.email || 'N/A';
        const userRoles =
            user.roles && user.roles.length > 0
                ? user.roles.join(', ')
                : 'Không có vai trò';

        // Update UI elements
        $('#userName').text(userName);
        $('#userEmail').text(userEmail);
        $('#userRoles').text(userRoles);
    }
}

/**
 * Setup quick action buttons based on user permissions
 */
function setupQuickActionButtons() {
    // Business Centers management
    if (abp.auth.isGranted('TikTok.BusinessCenters')) {
        $('#businessCenterBtn').show();
    } else {
        $('#businessCenterBtn').hide();
    }

    // Ad Accounts management
    if (abp.auth.isGranted('TikTok.AdAccounts')) {
        $('#adAccountBtn').show();
    } else {
        $('#adAccountBtn').hide();
    }

    // Balance Business Centers management
    if (abp.auth.isGranted('TikTok.BalanceBusinessCenters')) {
        $('#balanceBusinessCenterBtn').show();
    } else {
        $('#balanceBusinessCenterBtn').hide();
    }

    // Balance Ad Accounts management
    if (abp.auth.isGranted('TikTok.BalanceAdAccounts')) {
        $('#balanceAdAccountBtn').show();
    } else {
        $('#balanceAdAccountBtn').hide();
    }

    // Transactions management
    if (abp.auth.isGranted('TikTok.Transactions')) {
        $('#transactionBtn').show();
    } else {
        $('#transactionBtn').hide();
    }

    // Assets management
    if (abp.auth.isGranted('TikTok.Assets')) {
        $('#assetBtn').show();
    } else {
        $('#assetBtn').hide();
    }

    // Workflows Studio (assuming it requires admin permission)
    $('#workflowStudioBtn').hide();
    const user = abp.currentUser;
    if (
        user &&
        user.isAuthenticated &&
        user.roles &&
        user.roles.includes('admin')
    ) {
        $('#workflowStudioBtn').show();
    }

    // Notification Rules management
    if (abp.auth.isGranted('TikTok.NotificationRules')) {
        $('#notificationRulesBtn').show();
    } else {
        $('#notificationRulesBtn').hide();
    }
}

/**
 * Load dashboard statistics via AJAX
 */
function loadDashboardStatistics() {
    // Only load if user is authenticated
    if (!isUserAuthenticated()) {
        return;
    }

    // Show loading state
    showLoadingState();

    // Make AJAX request to get dashboard data
    $.ajax({
        url: '/api/dashboard/overview',
        method: 'GET',
        dataType: 'json',
        success: function (data) {
            updateDashboardUI(data);
        },
        error: function (xhr, status, error) {
            console.error('Error loading dashboard statistics:', error);
            showErrorState();
        },
    });
}

/**
 * Show loading state for all cards
 */
function showLoadingState() {
    $('#businessCenterCount').html('<i class="fas fa-spinner fa-spin"></i>');
    $('#adAccountCount').html('<i class="fas fa-spinner fa-spin"></i>');
    $('#campaignCount').html('<i class="fas fa-spinner fa-spin"></i>');
    $('#customerCount').html('<i class="fas fa-spinner fa-spin"></i>');
    $('#lastUpdated').text('Đang tải...');
}

/**
 * Show error state for all cards
 */
function showErrorState() {
    $('#businessCenterCount').html(
        '<i class="fas fa-exclamation-triangle text-danger"></i>'
    );
    $('#adAccountCount').html(
        '<i class="fas fa-exclamation-triangle text-danger"></i>'
    );
    $('#campaignCount').html(
        '<i class="fas fa-exclamation-triangle text-danger"></i>'
    );
    $('#customerCount').html(
        '<i class="fas fa-exclamation-triangle text-danger"></i>'
    );
    $('#lastUpdated').text('Lỗi tải dữ liệu');
}

/**
 * Update dashboard UI with received data
 */
function updateDashboardUI(data) {
    // Update main statistics cards
    $('#businessCenterCount').text(formatNumber(data.businessCenterCount));
    $('#adAccountCount').text(formatNumber(data.adAccountCount));
    $('#campaignCount').text(formatNumber(data.campaignCount));
    $('#customerCount').text(formatNumber(data.customerCount));

    // Update last updated time
    if (data.lastUpdated) {
        const lastUpdated = new Date(data.lastUpdated);
        $('#lastUpdated').text(lastUpdated.toLocaleString('vi-VN'));
    }

    // Add animation effect
    animateNumbers();
}

/**
 * Format number with thousand separators
 */
function formatNumber(num) {
    if (num === null || num === undefined) return '0';
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * Add animation effect to numbers
 */
function animateNumbers() {
    $('.h5.mb-0.font-weight-bold.text-gray-800').each(function () {
        const $this = $(this);
        const finalNumber = parseInt($this.text().replace(/,/g, ''));

        if (!isNaN(finalNumber)) {
            $this.prop('Counter', 0).animate(
                {
                    Counter: finalNumber,
                },
                {
                    duration: 1000,
                    easing: 'swing',
                    step: function (now) {
                        $this.text(formatNumber(Math.ceil(now)));
                    },
                }
            );
        }
    });
}

/**
 * Manual refresh function (can be called from UI)
 */
function refreshDashboard() {
    // Only refresh if user is authenticated
    if (!isUserAuthenticated()) {
        return;
    }

    loadDashboardStatistics();

    // Show refresh feedback
    const $refreshBtn = $('#refreshBtn');
    if ($refreshBtn.length) {
        const originalText = $refreshBtn.text();
        $refreshBtn.text('Đang tải...').prop('disabled', true);

        setTimeout(function () {
            $refreshBtn.text(originalText).prop('disabled', false);
        }, 2000);
    }
}

/**
 * Check if user has specific permission
 */
function hasPermission(permissionName) {
    return abp.auth.isGranted(permissionName);
}

/**
 * Get current user information
 */
function getCurrentUser() {
    return abp.currentUser;
}

/**
 * Check if user is in specific role
 */
function isInRole(roleName) {
    return (
        abp.currentUser &&
        abp.currentUser.roles &&
        abp.currentUser.roles.includes(roleName)
    );
}
