@page "/notification-test/monitor"
@using Microsoft.Extensions.Localization
@using TikTok.Localization
@inject IStringLocalizer<TikTokResource> L

<div class="container py-4">
    <div class="card border-warning">
        <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fa-solid fa-flask me-2"></i>Notification Test Monitor</h5>
            <span class="badge bg-dark">TEST ONLY</span>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fa-solid fa-exclamation-triangle me-2"></i>
                <strong>Lưu ý:</strong> Đ<PERSON><PERSON> là hệ thống test chung cho tất cả loại notification.
                Nhập Campaign ID để tìm tất cả product creative của campaign đó, đổi trạng thái sang problematic và gửi thông báo.
            </div>

            <form id="testForm" class="row g-3">
                <div class="col-md-4">
                    <label for="campaignId" class="form-label">Campaign ID <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="campaignId" name="campaignId" placeholder="Enter Campaign ID" required />
                    <small class="form-text text-muted">Campaign ID bắt buộc để test</small>
                </div>
                <div class="col-md-3">
                    <label for="notificationType" class="form-label">Notification Type</label>
                    <select class="form-control" id="notificationType" name="notificationType">
                        <option value="GmvMaxCreativeStatusChange">GMV Max Creative Status Change</option>
                    </select>
                    <small class="form-text text-muted">Loại notification cần test</small>
                </div>
                <div class="col-md-3">
                    <label for="maxDays" class="form-label">Max Days</label>
                    <input type="number" class="form-control" id="maxDays" name="maxDays" value="1" min="1" max="10" />
                    <small class="form-text text-muted">Giới hạn số ngày gần nhất</small>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-warning w-100">
                        <i class="fa-solid fa-flask me-1"></i> Test
                    </button>
                </div>
            </form>

            <hr />

            <div id="resultArea" class="small text-muted" style="white-space: pre-wrap; max-height: 400px; overflow-y: auto;"></div>

            <div class="mt-3">
                <a id="viewUnreadLink" class="btn btn-outline-secondary btn-sm" href="#" target="_blank">
                    <i class="fa-regular fa-eye me-1"></i> View unread notifications
                </a>
                <button type="button" class="btn btn-outline-info btn-sm ms-2" onclick="NotificationTestMonitor.clearResult()">
                    <i class="fa-solid fa-trash me-1"></i> Clear Result
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="/js/notificationTest/monitor.js?v=@DateTime.Now.Ticks"></script>
}
