﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using TikTok.Localization;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace TikTok.Web.Pages;

/* Inherit your PageModel classes from this class.
 */
public abstract class TikTokPageModel : AbpPageModel
{
    protected TikTokPageModel()
    {
        LocalizationResourceType = typeof(TikTokResource);
    }

    /// <summary>
    /// Check if user is authenticated and redirect to login if not
    /// </summary>
    /// <returns>Redirect result if not authenticated, null if authenticated</returns>
    protected virtual IActionResult CheckAuthenticationAndRedirect()
    {
        if (!CurrentUser.IsAuthenticated)
        {
            var returnUrl = HttpContext.Request.Path + HttpContext.Request.QueryString;
            return Redirect($"/Account/Login?returnUrl={Uri.EscapeDataString(returnUrl)}");
        }
        return null;
    }

    /// <summary>
    /// Async version of authentication check
    /// </summary>
    /// <returns>Redirect result if not authenticated, null if authenticated</returns>
    protected virtual Task<IActionResult> CheckAuthenticationAndRedirectAsync()
    {
        return Task.FromResult(CheckAuthenticationAndRedirect());
    }

    /// <summary>
    /// Check if user is authenticated (simple boolean check)
    /// </summary>
    /// <returns>True if authenticated, false otherwise</returns>
    protected virtual bool IsUserAuthenticated()
    {
        return CurrentUser.IsAuthenticated;
    }

    /// <summary>
    /// Get the current return URL from query parameters
    /// </summary>
    /// <returns>Return URL if present, otherwise current page URL</returns>
    protected virtual string GetReturnUrl()
    {
        var returnUrl = HttpContext.Request.Query["returnUrl"].ToString();
        if (string.IsNullOrEmpty(returnUrl))
        {
            returnUrl = HttpContext.Request.Path + HttpContext.Request.QueryString;
        }
        return returnUrl;
    }
}
