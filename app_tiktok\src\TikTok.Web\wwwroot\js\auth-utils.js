/**
 * TikTok Authentication Utilities
 * Provides common authentication checking and redirect functionality
 */

(function (window) {
    'use strict';

    // Create TikTok namespace if it doesn't exist
    window.TikTok = window.TikTok || {};
    window.TikTok.Auth = window.TikTok.Auth || {};

    /**
     * Check if user is authenticated
     * @returns {boolean} True if authenticated, false otherwise
     */
    function isAuthenticated() {
        return abp.currentUser && abp.currentUser.isAuthenticated;
    }

    /**
     * Get current user information
     * @returns {object} Current user object from ABP
     */
    function getCurrentUser() {
        return abp.currentUser || {};
    }

    /**
     * Check authentication and redirect to login if not authenticated
     * @param {string} returnUrl - Optional return URL, defaults to current page
     * @returns {boolean} True if authenticated, false if redirected
     */
    function requireAuthenticationOrRedirect(returnUrl) {
        if (!isAuthenticated()) {
            redirectToLogin(returnUrl);
            return false;
        }
        return true;
    }

    /**
     * Redirect to login page with return URL
     * @param {string} returnUrl - Optional return URL, defaults to current page
     */
    function redirectToLogin(returnUrl) {
        if (!returnUrl) {
            returnUrl = window.location.pathname + window.location.search;
        }
        const encodedReturnUrl = encodeURIComponent(returnUrl);
        window.location.href = `/Account/Login?returnUrl=${encodedReturnUrl}`;
    }

    /**
     * Execute callback only if user is authenticated, otherwise redirect to login
     * @param {function} callback - Function to execute if authenticated
     * @param {string} returnUrl - Optional return URL
     */
    function requireAuthentication(callback, returnUrl) {
        if (requireAuthenticationOrRedirect(returnUrl)) {
            if (typeof callback === 'function') {
                callback();
            }
        }
    }

    /**
     * Check if user has specific permission
     * @param {string} permission - Permission name to check
     * @returns {boolean} True if user has permission, false otherwise
     */
    function hasPermission(permission) {
        return abp.auth.isGranted(permission);
    }

    /**
     * Execute callback only if user has required permission
     * @param {string} permission - Required permission
     * @param {function} callback - Function to execute if permission granted
     * @param {function} deniedCallback - Optional function to execute if permission denied
     */
    function requirePermission(permission, callback, deniedCallback) {
        if (!isAuthenticated()) {
            redirectToLogin();
            return;
        }

        if (hasPermission(permission)) {
            if (typeof callback === 'function') {
                callback();
            }
        } else {
            if (typeof deniedCallback === 'function') {
                deniedCallback();
            } else {
                abp.notify.error(
                    'You do not have permission to perform this action.'
                );
            }
        }
    }

    /**
     * Show authentication required message
     * @param {string} message - Optional custom message
     */
    function showAuthenticationRequired(message) {
        const defaultMessage = 'You must be logged in to access this feature.';
        abp.message.info(message || defaultMessage, 'Authentication Required');
    }

    /**
     * Handle authentication errors from AJAX calls
     * @param {object} error - Error object from AJAX call
     */
    function handleAuthenticationError(error) {
        if (error.status === 401) {
            showAuthenticationRequired(
                'Your session has expired. Please log in again.'
            );
            setTimeout(() => {
                redirectToLogin();
            }, 2000);
        } else if (error.status === 403) {
            abp.notify.error(
                'You do not have permission to perform this action.'
            );
        }
    }

    /**
     * Setup automatic authentication handling for AJAX calls
     */
    function setupAutoAuthHandling() {
        // Wait for ABP to be fully initialized before setting up auth handling
        if (typeof abp === 'undefined' || !abp.ajax) {
            // If ABP is not ready, wait and try again
            setTimeout(setupAutoAuthHandling, 100);
            return;
        }

        // Store the original ajax function and its properties
        const originalAjax = abp.ajax;
        const originalAjaxSendHandler = abp.ajax.ajaxSendHandler;

        // Create a new ajax function that preserves all original properties
        const newAjax = function (options) {
            const originalError = options.error;
            options.error = function (error) {
                handleAuthenticationError(error);
                if (typeof originalError === 'function') {
                    originalError(error);
                }
            };
            return originalAjax(options);
        };

        // Copy all properties from original ajax to new ajax function
        Object.setPrototypeOf(newAjax, originalAjax);
        Object.assign(newAjax, originalAjax);

        // Ensure ajaxSendHandler is preserved
        if (originalAjaxSendHandler) {
            newAjax.ajaxSendHandler = originalAjaxSendHandler;
        }

        // Replace the ajax function
        abp.ajax = newAjax;
    }

    // Public API
    window.TikTok.Auth = {
        isAuthenticated: isAuthenticated,
        getCurrentUser: getCurrentUser,
        requireAuthenticationOrRedirect: requireAuthenticationOrRedirect,
        redirectToLogin: redirectToLogin,
        requireAuthentication: requireAuthentication,
        hasPermission: hasPermission,
        requirePermission: requirePermission,
        showAuthenticationRequired: showAuthenticationRequired,
        handleAuthenticationError: handleAuthenticationError,
        setupAutoAuthHandling: setupAutoAuthHandling,
    };

    // Initialize auto auth handling when document is ready and ABP is available
    $(document).ready(function () {
        // Wait for ABP to be fully loaded
        if (typeof abp !== 'undefined' && abp.event) {
            // Use ABP's configuration initialized event if available
            abp.event.on('abp.configurationInitialized', function () {
                setupAutoAuthHandling();
            });
        } else {
            // Fallback: try to setup after a short delay
            setTimeout(setupAutoAuthHandling, 500);
        }
    });
})(window);
