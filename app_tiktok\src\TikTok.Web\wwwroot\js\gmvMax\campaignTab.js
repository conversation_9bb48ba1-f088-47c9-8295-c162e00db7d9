/**
 * Campaign Tab Manager
 * Copy HOÀN TOÀN logic từ factGmvMaxCampaign.js và factGmvMaxProduct.js
 */

class CampaignTabManager {
    constructor(config, dataAggregator) {
        this.config = config;
        this.dataAggregator = dataAggregator;
        this.roiCards = null;

        // ✅ COPY HOÀN TOÀN từ factGmvMaxCampaign.js và factGmvMaxProduct.js
        this.productPivotTable = null;
        this.livePivotTable = null;

        this.currentFilters = {
            fromDate: config.dateRange.from,
            toDate: config.dateRange.to,
            currency: this.getCurrentCurrency(),
            shopIds: [],
            searchText: '',
        };

        this.isInitialized = false;
    }

    /**
     * Get current currency from localStorage (same as FactGmvMaxCampaign)
     */
    getCurrentCurrency() {
        return (
            (typeof localStorage !== 'undefined'
                ? localStorage.getItem('tiktok_currency')
                : null) || 'USD'
        );
    }

    /**
     * Initialize Campaign Tab
     */
    async init() {
        try {
            // Setup filter components
            this.setupFilterComponents();

            // Setup event listeners
            this.setupEventListeners();

            // Setup currency change listener (like FactGmvMaxCampaign)
            this.setupCurrencyChangeListener();

            // Initialize ROI Cards component
            this.roiCards = new RoiCardsComponent();

            // Load dropdown data for filters
            await this.loadDropdownData();

            this.isInitialized = true;
        } catch (error) {
            console.error('❌ Error initializing Campaign Tab:', error);
            throw error;
        }
    }

    /**
     * Load campaign data
     */
    async loadData(filters = null) {
        try {
            if (!this.isInitialized) {
                await this.init();
            }

            // Update filters if provided
            if (filters) {
                this.currentFilters = { ...this.currentFilters, ...filters };
            }
            // Show loading states
            this.showLoadingState();

            // 🔥 NEW: 3 separate API calls for improved data sourcing

            // 1. Product ROI (từ FactGmvMaxCampaignEntity - PRODUCT)
            const productROIData = await this.dataAggregator.fetchCampaignData({
                ...this.currentFilters,
                shoppingAdsType: 'PRODUCT',
            });

            // 2. Live ROI (từ FactGmvMaxCampaignEntity - LIVE)
            const liveROIData = await this.dataAggregator.fetchCampaignData({
                ...this.currentFilters,
                shoppingAdsType: 'LIVE',
            });

            // 3. Product Pivot (từ FactGmvMaxProductEntity)
            const productPivotData = await this.dataAggregator.fetchProductData(
                this.currentFilters
            );

            // 🆕 Load separate ROI analyses using dedicated APIs
            await this.loadProductRoiAnalysis(this.currentFilters);
            await this.loadLiveRoiAnalysis(this.currentFilters);

            // Load pivot tables with real data
            await this.loadPivotTables(productPivotData, liveROIData);

            // Hide loading states
            this.hideLoadingState();
        } catch (error) {
            console.error('❌ Error loading Campaign Tab data:', error);
            this.hideLoadingState();
            this.showErrorMessage('Lỗi tải dữ liệu campaign: ' + error.message);
        }
    }

    /**
     * 🆕 Load Product ROI analysis cards using dedicated API
     */
    async loadProductRoiAnalysis(filters) {
        try {
            // 🆕 Fetch ROI analysis from dedicated API
            const productRoiAnalysis =
                await this.dataAggregator.fetchRoiAnalysis({
                    ...filters,
                    shoppingAdsType: 'PRODUCT',
                });

            // Render ROI cards to Product section container
            this.roiCards.renderToContainer(
                'product-roi-cards-container',
                productRoiAnalysis,
                'PRODUCT'
            );
        } catch (error) {
            console.error('❌ Error loading Product ROI analysis:', error);
            throw error;
        }
    }

    /**
     * 🆕 Load Live ROI analysis cards using dedicated API
     */
    async loadLiveRoiAnalysis(filters) {
        try {
            // 🆕 Fetch ROI analysis from dedicated API
            const liveRoiAnalysis = await this.dataAggregator.fetchRoiAnalysis({
                ...filters,
                shoppingAdsType: 'LIVE',
            });

            // Render ROI cards to Live section container
            this.roiCards.renderToContainer(
                'live-roi-cards-container',
                liveRoiAnalysis,
                'LIVE'
            );
        } catch (error) {
            console.error('❌ Error loading Live ROI analysis:', error);
            throw error;
        }
    }

    /**
     * @deprecated Legacy method - replaced by separate Product/Live methods
     */
    async loadRoiAnalysis(campaignData) {
        console.warn(
            '⚠️ loadRoiAnalysis() is deprecated. Use loadProductRoiAnalysis() and loadLiveRoiAnalysis() instead.'
        );
    }

    /**
     * Setup filter components (similar to VideoTabManager)
     */
    setupFilterComponents() {
        try {
            // Initialize Syncfusion DateRangePicker
            const dateRangePicker = new ej.calendars.DateRangePicker({
                startDate: new Date(this.currentFilters.fromDate),
                endDate: new Date(this.currentFilters.toDate),
                format: 'dd/MM/yyyy',
                placeholder: 'Chọn khoảng thời gian',
                change: (args) => {
                    if (args.startDate && args.endDate) {
                        this.currentFilters.fromDate = args.startDate
                            .toISOString()
                            .split('T')[0];
                        this.currentFilters.toDate = args.endDate
                            .toISOString()
                            .split('T')[0];
                    }
                },
            });
            dateRangePicker.appendTo('#campaign-date-range-picker');

            // Initialize Quick Date Dropdown
            const quickDateOptions = [
                { text: 'Chọn khoảng', value: '' },
                { text: 'Hôm nay', value: 'today' },
                { text: '7 ngày', value: '7d' },
                { text: '30 ngày', value: '30d' },
                { text: 'Quý này', value: '90d' },
            ];

            const quickDateDropdown = new ej.dropdowns.DropDownList({
                dataSource: quickDateOptions,
                fields: { text: 'text', value: 'value' },
                placeholder: 'Lọc nhanh',
                change: (args) => {
                    if (args.value) {
                        this.setQuickDateFilter(
                            args.value,
                            'campaign-date-range-picker'
                        );
                    }
                },
            });
            quickDateDropdown.appendTo('#campaign-quick-date-dropdown');

            // Initialize Text Search
            const keywordSearch = document.getElementById(
                'campaign-keyword-search'
            );
            if (keywordSearch) {
                keywordSearch.addEventListener('input', (e) => {
                    this.currentFilters.searchText = e.target.value || '';
                });

                // Add Enter key support for quick search
                keywordSearch.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.loadData(this.currentFilters);
                    }
                });
            }

            // Initialize Shop MultiSelect
            const shopMultiSelect = new ej.dropdowns.MultiSelect({
                placeholder: 'Chọn shop',
                mode: 'CheckBox',
                showCheckBox: true,
                showSelectAll: true,
                enableSelectionOrder: false,
                change: (args) => {
                    this.currentFilters.shopIds = args.value || [];
                },
            });
            shopMultiSelect.appendTo('#campaign-shop-multiselect');
        } catch (error) {
            console.error(
                '❌ Error setting up campaign filter components:',
                error
            );
        }
    }

    /**
     * Setup event listeners for campaign filters
     */
    setupEventListeners() {
        // Apply filters button
        const applyFiltersBtn = document.getElementById(
            'apply-campaign-filters'
        );
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.loadData(this.currentFilters);
            });
        }

        // Clear filters button
        const clearFiltersBtn = document.getElementById(
            'clear-campaign-filters'
        );
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }
    }

    /**
     * Setup currency change listener (like FactGmvMaxCampaign)
     */
    setupCurrencyChangeListener() {
        document.addEventListener('currencyChanged', (e) => {
            // Update current filters
            this.currentFilters.currency = e.detail.currency;

            // Reload data with new currency
            this.loadData(this.currentFilters);
        });
    }

    /**
     * Load dropdown data for filters using new APIs
     */
    async loadDropdownData() {
        try {
            // Load only shops data (simplified)
            const shopsResponse = await fetch('/api/dim-stores?format=simple');

            // Check if response is ok
            if (!shopsResponse.ok) {
                console.warn('Failed to load shops:', shopsResponse.statusText);
                return;
            }

            // Parse JSON response
            const shops = await shopsResponse.json();

            // Update Shop MultiSelect
            const shopMultiSelect = document.querySelector(
                '#campaign-shop-multiselect'
            );
            if (
                shopMultiSelect &&
                shopMultiSelect.ej2_instances &&
                shops.length > 0
            ) {
                const multiSelectObj = shopMultiSelect.ej2_instances[0];
                multiSelectObj.dataSource = shops; // API already returns {text, value} format
                multiSelectObj.dataBind();
            } else if (shops.length === 0) {
                this.showEmptyDropdownState();
            }
        } catch (error) {
            console.error('❌ Error loading dropdown data:', error);

            // Fallback: Try to show empty state gracefully
            this.showEmptyDropdownState();
        }
    }

    /**
     * Show empty state when dropdown data fails to load
     */
    showEmptyDropdownState() {
        try {
            // Update Shop MultiSelect with empty message
            const shopMultiSelect = document.querySelector(
                '#campaign-shop-multiselect'
            );
            if (shopMultiSelect && shopMultiSelect.ej2_instances) {
                const multiSelectObj = shopMultiSelect.ej2_instances[0];
                multiSelectObj.dataSource = [
                    { text: 'Không có dữ liệu shop', value: '' },
                ];
                multiSelectObj.dataBind();
            }
        } catch (error) {
            console.error('❌ Error showing empty dropdown state:', error);
        }
    }

    /**
     * Clear all campaign filters
     */
    clearFilters() {
        this.currentFilters = {
            fromDate: this.config.dateRange.from,
            toDate: this.config.dateRange.to,
            currency: this.config.currency,
            shopIds: [],
            searchText: '',
        };

        // Reset filter components
        try {
            // Reset DateRangePicker
            const dateRangePicker = document.getElementById(
                'campaign-date-range-picker'
            );
            if (dateRangePicker && dateRangePicker.ej2_instances) {
                const dateRangeObj = dateRangePicker.ej2_instances[0];
                dateRangeObj.startDate = new Date(this.config.dateRange.from);
                dateRangeObj.endDate = new Date(this.config.dateRange.to);
            }

            // Reset Quick Date Dropdown
            const quickDateDropdown = document.getElementById(
                'campaign-quick-date-dropdown'
            );
            if (quickDateDropdown && quickDateDropdown.ej2_instances) {
                const quickDateObj = quickDateDropdown.ej2_instances[0];
                quickDateObj.value = '';
            }

            // Reset Text Search
            const keywordSearch = document.getElementById(
                'campaign-keyword-search'
            );
            if (keywordSearch) {
                keywordSearch.value = '';
            }

            // Reset Shop MultiSelect
            const shopMultiSelect = document.getElementById(
                'campaign-shop-multiselect'
            );
            if (shopMultiSelect && shopMultiSelect.ej2_instances) {
                const shopObj = shopMultiSelect.ej2_instances[0];
                shopObj.value = [];
            }
        } catch (error) {
            console.error(
                '❌ Error resetting campaign filter components:',
                error
            );
        }

        // Reload data
        this.loadData();
    }

    /**
     * Set quick date filter (copy from VideoTabManager)
     */
    setQuickDateFilter(value, dateRangePickerId) {
        try {
            const dateRangePicker = document.getElementById(dateRangePickerId);
            if (!dateRangePicker || !dateRangePicker.ej2_instances) {
                console.warn('DateRangePicker not found or not initialized');
                return;
            }

            const dateRangeObj = dateRangePicker.ej2_instances[0];
            const today = new Date();
            let startDate, endDate;

            switch (value) {
                case 'today':
                    startDate = new Date(today);
                    endDate = new Date(today);
                    break;
                case '7d':
                    startDate = new Date(
                        today.getTime() - 6 * 24 * 60 * 60 * 1000
                    );
                    endDate = new Date(today);
                    break;
                case '30d':
                    startDate = new Date(
                        today.getTime() - 29 * 24 * 60 * 60 * 1000
                    );
                    endDate = new Date(today);
                    break;
                case '90d':
                    startDate = new Date(
                        today.getTime() - 89 * 24 * 60 * 60 * 1000
                    );
                    endDate = new Date(today);
                    break;
                default:
                    return;
            }

            // Update DateRangePicker
            if (dateRangeObj && startDate && endDate) {
                dateRangeObj.startDate = startDate;
                dateRangeObj.endDate = endDate;

                // Update current filters
                this.currentFilters.fromDate = startDate
                    .toISOString()
                    .split('T')[0];
                this.currentFilters.toDate = endDate
                    .toISOString()
                    .split('T')[0];
            }
        } catch (error) {
            console.error(
                '❌ Error setting campaign quick date filter:',
                error
            );
        }
    }

    /**
     * ✅ Load pivot tables với real data từ APIs - COPY pattern từ màn hình hiện có
     */
    async loadPivotTables(productPivotData, liveROIData) {
        try {
            // ✅ Set data vào global scope như factGmvMaxProduct.js
            window.initialGmvMaxProductDataForGmvMax = productPivotData;

            // ✅ Set Live Campaign data vào global scope như factGmvMaxCampaign.js
            window.initialGmvMaxCampaignDataForGmvMax = liveROIData;

            // ✅ Initialize Product Pivot giống hệt factGmvMaxProduct.js
            await this.initializeProductPivotTable();

            // ✅ Initialize Live Campaign Pivot giống hệt factGmvMaxCampaign.js
            await this.initializeLivePivotTable();
        } catch (error) {
            console.error(
                '❌ Error loading pivot tables with real data:',
                error
            );
            throw error;
        }
    }

    /**
     * ✅ COPY HOÀN TOÀN: Initialize Product Pivot Table như factGmvMaxProduct.js
     */
    async initializeProductPivotTable() {
        try {
            // Show loading, hide pivot
            const loadingElement = document.getElementById(
                'product-pivot-loading'
            );
            const pivotElement = document.getElementById(
                'productCampaignPivotTable'
            );

            if (loadingElement) loadingElement.style.display = 'block';
            if (pivotElement) pivotElement.style.display = 'none';

            // ✅ COPY class TiktokGmvMaxProductPivotTable từ factGmvMaxProduct.js
            this.productPivotTable = new TiktokGmvMaxProductPivotTableForGmvMax(
                'productCampaignPivotTable'
            );
            await this.productPivotTable.initial();

            // Hide loading, show pivot
            if (loadingElement) loadingElement.style.display = 'none';
            if (pivotElement) pivotElement.style.display = 'block';
        } catch (error) {
            console.error('❌ Error initializing Product Pivot Table:', error);

            // Hide loading on error
            const loadingElement = document.getElementById(
                'product-pivot-loading'
            );
            if (loadingElement) loadingElement.style.display = 'none';

            throw error;
        }
    }

    /**
     * ✅ COPY HOÀN TOÀN: Initialize Live Campaign Pivot Table như factGmvMaxCampaign.js
     */
    async initializeLivePivotTable() {
        try {
            // Show loading, hide pivot
            const loadingElement =
                document.getElementById('live-pivot-loading');
            const pivotElement = document.getElementById(
                'liveCampaignPivotTable'
            );

            if (loadingElement) loadingElement.style.display = 'block';
            if (pivotElement) pivotElement.style.display = 'none';

            // ✅ COPY class TiktokGmvMaxCampaignPivotTable từ factGmvMaxCampaign.js
            this.livePivotTable = new TiktokGmvMaxCampaignPivotTableForGmvMax(
                'liveCampaignPivotTable'
            );
            await this.livePivotTable.initial();

            // Hide loading, show pivot
            if (loadingElement) loadingElement.style.display = 'none';
            if (pivotElement) pivotElement.style.display = 'block';
        } catch (error) {
            console.error(
                '❌ Error initializing Live Campaign Pivot Table:',
                error
            );

            // Hide loading on error
            const loadingElement =
                document.getElementById('live-pivot-loading');
            if (loadingElement) loadingElement.style.display = 'none';

            throw error;
        }
    }

    /**
     * Show loading state
     */
    showLoadingState() {
        const roiContainer = document.getElementById('roi-cards-container');
        if (roiContainer) {
            roiContainer.innerHTML = `
                <div class="col-12 text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Đang tải phân tích ROI...</p>
                </div>
            `;
        }
    }

    /**
     * Hide loading state
     */
    hideLoadingState() {
        // Loading states will be replaced by actual content
    }

    /**
     * Show error message
     */
    showErrorMessage(message) {
        const roiContainer = document.getElementById('roi-cards-container');
        if (roiContainer) {
            roiContainer.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${message}
                    </div>
                </div>
            `;
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        if (this.productPivotTable && this.productPivotTable.pivotTableObj) {
            this.productPivotTable.pivotTableObj.refresh();
        }
        if (this.livePivotTable && this.livePivotTable.pivotTableObj) {
            this.livePivotTable.pivotTableObj.refresh();
        }
    }

    /**
     * Destroy and cleanup
     */
    destroy() {
        if (this.productPivotTable && this.productPivotTable.pivotTableObj) {
            this.productPivotTable.pivotTableObj.destroy();
        }
        if (this.livePivotTable && this.livePivotTable.pivotTableObj) {
            this.livePivotTable.pivotTableObj.destroy();
        }
        if (this.roiCards) {
            this.roiCards.destroy();
        }
    }
}

// ✅ Make campaignTabManager globally accessible for ROI card modal details
window.CampaignTabManager = CampaignTabManager;
