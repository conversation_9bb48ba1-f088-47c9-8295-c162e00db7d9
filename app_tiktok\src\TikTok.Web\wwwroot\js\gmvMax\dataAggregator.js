/**
 * GMV Max Data Aggregator
 * Handles data fetching and aggregation from multiple APIs
 */

class GmvMaxDataAggregator {
    constructor(config) {
        this.config = config;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    }

    /**
     * Get campaign data (both Product and Live)
     */
    async getCampaignData(filters = {}) {
        try {
            const cacheKey = `campaign_${JSON.stringify(filters)}`;
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }

            // Fetch data from both APIs in parallel
            const [campaignResponse, productResponse] = await Promise.all([
                this.fetchCampaignData(filters),
                this.fetchProductData(filters),
            ]);

            const aggregatedData = {
                campaigns: campaignResponse.factGmvMaxCampaigns || [],
                products: productResponse.factGmvMaxProducts || [],
                dimensions: {
                    stores:
                        campaignResponse.dimStores ||
                        productResponse.dimStores ||
                        [],
                    campaigns:
                        campaignResponse.dimCampaigns ||
                        productResponse.dimCampaigns ||
                        [],
                    businessCenters:
                        campaignResponse.dimBusinessCenters ||
                        productResponse.dimBusinessCenters ||
                        [],
                    adAccounts:
                        campaignResponse.dimAdAccounts ||
                        productResponse.dimAdAccounts ||
                        [],
                    dates:
                        campaignResponse.dimDates ||
                        productResponse.dimDates ||
                        [],
                },
                metadata: {
                    campaignCount:
                        campaignResponse.factGmvMaxCampaigns?.length || 0,
                    productCount:
                        productResponse.factGmvMaxProducts?.length || 0,
                    fromDate: filters.fromDate || this.config.dateRange.from,
                    toDate: filters.toDate || this.config.dateRange.to,
                    currency: filters.currency || this.config.currency,
                },
            };

            this.setCache(cacheKey, aggregatedData);
            return aggregatedData;
        } catch (error) {
            console.error('❌ Error fetching campaign data:', error);
            throw new Error(`Lỗi tải dữ liệu campaign: ${error.message}`);
        }
    }

    /**
     * Get video creative data
     */
    async getVideoData(filters = {}) {
        try {
            const cacheKey = `video_${JSON.stringify(filters)}`;
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }

            const response = await this.fetchCreativeData(filters);

            const aggregatedData = {
                creatives: response.items || [],
                totalCount: response.totalCount || 0,
                metadata: {
                    fromDate: filters.fromDate || this.config.dateRange.from,
                    toDate: filters.toDate || this.config.dateRange.to,
                    pageSize: filters.maxResultCount || 20,
                    currentPage:
                        Math.floor(
                            (filters.skipCount || 0) /
                                (filters.maxResultCount || 20)
                        ) + 1,
                },
            };

            this.setCache(cacheKey, aggregatedData);
            return aggregatedData;
        } catch (error) {
            console.error('❌ Error fetching video data:', error);
            throw new Error(`Lỗi tải dữ liệu video: ${error.message}`);
        }
    }

    /**
     * Get campaign IDs for dropdown filters
     */
    async getCampaignIds() {
        try {
            const cacheKey = 'campaign_ids';
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }

            const response = await fetch(
                `${this.config.apiEndpoints.creative}/campaign-ids`,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );

            if (!response.ok) {
                throw new Error(
                    `HTTP ${response.status}: ${response.statusText}`
                );
            }

            const campaignIds = await response.json();
            this.setCache(cacheKey, campaignIds);
            return campaignIds;
        } catch (error) {
            console.error('❌ Error fetching campaign IDs:', error);
            throw new Error(`Lỗi tải danh sách campaign: ${error.message}`);
        }
    }

    /**
     * 🆕 Fetch summary data from dedicated API (like FactGmvMaxCampaign)
     */
    async fetchSummaryData(filters) {
        try {
            // Get currency from localStorage like FactGmvMaxCampaign
            const currency =
                filters.currency ||
                (typeof localStorage !== 'undefined'
                    ? localStorage.getItem('tiktok_currency')
                    : null) ||
                this.config.currency ||
                'USD';
            const params = new URLSearchParams({
                currency: currency,
                fromDate: filters.fromDate || this.config.dateRange.from,
                toDate: filters.toDate || this.config.dateRange.to,
            });

            // Add shoppingAdsType filter for PRODUCT/LIVE specific summaries
            if (filters.shoppingAdsType) {
                params.append('shoppingAdsType', filters.shoppingAdsType);
            }

            // Add other filters
            if (filters.searchText) {
                params.append('searchText', filters.searchText);
            }
            if (filters.shopIds && filters.shopIds.length > 0) {
                filters.shopIds.forEach((id) => params.append('shopIds', id));
            }

            const response = await fetch(
                `/api/fact-gmv-max-campaign/summary?${params}`,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );

            if (!response.ok) {
                throw new Error(
                    `HTTP ${response.status}: ${response.statusText}`
                );
            }

            const summaryData = await response.json();

            return summaryData;
        } catch (error) {
            console.error('❌ Error fetching summary data:', error);
            throw error;
        }
    }

    /**
     * 🆕 Fetch ROI analysis data from dedicated API
     */
    async fetchRoiAnalysis(filters, threshold = { good: 3.0, poor: 1.5 }) {
        try {
            // Get currency from localStorage like FactGmvMaxCampaign
            const currency =
                filters.currency ||
                (typeof localStorage !== 'undefined'
                    ? localStorage.getItem('tiktok_currency')
                    : null) ||
                this.config.currency ||
                'USD';

            const params = new URLSearchParams({
                currency: currency,
                fromDate: filters.fromDate || this.config.dateRange.from,
                toDate: filters.toDate || this.config.dateRange.to,
                goodThreshold: threshold.good,
                poorThreshold: threshold.poor,
            });

            // Add shoppingAdsType filter for PRODUCT/LIVE specific ROI
            if (filters.shoppingAdsType) {
                params.append('shoppingAdsType', filters.shoppingAdsType);
            }

            // Add other filters
            if (filters.searchText) {
                params.append('searchText', filters.searchText);
            }
            if (filters.shopIds && filters.shopIds.length > 0) {
                filters.shopIds.forEach((id) => params.append('shopIds', id));
            }

            // ✅ Use campaigns-by-roi API for accurate counts (same as modal)
            const [goodRoiResponse, poorRoiResponse, summaryResponse] =
                await Promise.all([
                    fetch(
                        `/api/fact-gmv-max-campaign/campaigns-by-roi?${params}&roiCategory=good`,
                        {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' },
                        }
                    ),
                    fetch(
                        `/api/fact-gmv-max-campaign/campaigns-by-roi?${params}&roiCategory=poor`,
                        {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' },
                        }
                    ),
                    fetch(`/api/fact-gmv-max-campaign/summary?${params}`, {
                        method: 'GET',
                        headers: { 'Content-Type': 'application/json' },
                    }),
                ]);

            if (
                !goodRoiResponse.ok ||
                !poorRoiResponse.ok ||
                !summaryResponse.ok
            ) {
                throw new Error(
                    `HTTP Error: ${goodRoiResponse.status}, ${poorRoiResponse.status}, ${summaryResponse.status}`
                );
            }

            const [goodRoiData, poorRoiData, summaryData] = await Promise.all([
                goodRoiResponse.json(),
                poorRoiResponse.json(),
                summaryResponse.json(),
            ]);

            // ✅ Combine real ROI counts with summary metrics
            const totalCampaigns = goodRoiData.count + poorRoiData.count;
            const goodPercentage =
                totalCampaigns > 0
                    ? ((goodRoiData.count / totalCampaigns) * 100).toFixed(1)
                    : '0';
            const poorPercentage =
                totalCampaigns > 0
                    ? ((poorRoiData.count / totalCampaigns) * 100).toFixed(1)
                    : '0';

            return {
                goodRoi: {
                    count: goodRoiData.count, // ← REAL count from API
                    items: goodRoiData.campaigns.slice(0, 10), // Preview items
                    threshold: goodRoiData.threshold,
                },
                poorRoi: {
                    count: poorRoiData.count, // ← REAL count from API
                    items: poorRoiData.campaigns.slice(0, 10), // Preview items
                    threshold: poorRoiData.threshold,
                },
                summary: {
                    total: summaryData.campaignCount || 0,
                    totalCost: summaryData.totalCost || 0,
                    totalRevenue: summaryData.totalGrossRevenue || 0,
                    goodPercentage: goodPercentage,
                    poorPercentage: poorPercentage,
                },
            };
        } catch (error) {
            console.error('❌ Error fetching ROI analysis:', error);
            // Return fallback data structure
            return {
                goodRoi: {
                    count: 0,
                    items: [],
                    threshold: `>= ${threshold.good}`,
                },
                poorRoi: {
                    count: 0,
                    items: [],
                    threshold: `< ${threshold.poor}`,
                },
                summary: {
                    total: 0,
                    totalCost: 0,
                    totalRevenue: 0,
                    goodPercentage: 0,
                    poorPercentage: 0,
                },
            };
        }
    }

    /**
     * 🆕 Fetch ROI campaign details for modal
     */
    async fetchRoiCampaignDetails(filters, roiCategory) {
        try {
            const currency =
                filters.currency ||
                (typeof localStorage !== 'undefined'
                    ? localStorage.getItem('tiktok_currency')
                    : null) ||
                this.config.currency ||
                'USD';

            const params = new URLSearchParams({
                currency: currency,
                fromDate: filters.fromDate || this.config.dateRange.from,
                toDate: filters.toDate || this.config.dateRange.to,
                roiCategory: roiCategory,
            });

            // Add shoppingAdsType filter
            if (filters.shoppingAdsType) {
                params.append('shoppingAdsType', filters.shoppingAdsType);
            }

            // Add other filters
            if (filters.searchText) {
                params.append('searchText', filters.searchText);
            }
            if (filters.shopIds && filters.shopIds.length > 0) {
                filters.shopIds.forEach((id) => params.append('shopIds', id));
            }

            const response = await fetch(
                `/api/fact-gmv-max-campaign/campaigns-by-roi?${params}`,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );

            if (!response.ok) {
                throw new Error(
                    `HTTP ${response.status}: ${response.statusText}`
                );
            }

            const data = await response.json();

            return {
                count: data.count,
                items: data.campaigns,
                threshold: data.threshold,
            };
        } catch (error) {
            console.error(
                `❌ Error fetching ${roiCategory} ROI campaign details:`,
                error
            );
            return {
                count: 0,
                items: [],
                threshold: roiCategory === 'good' ? '>= 3.0' : '< 1.5',
            };
        }
    }

    /**
     * @deprecated Legacy method - no longer needed since we use campaigns-by-roi API
     */
    transformSummaryToRoi(summaryData, threshold) {
        console.warn(
            '⚠️ transformSummaryToRoi() is deprecated. Using campaigns-by-roi API instead.'
        );
        return {
            goodRoi: { count: 0, items: [], threshold: `>= ${threshold.good}` },
            poorRoi: { count: 0, items: [], threshold: `< ${threshold.poor}` },
            summary: {
                total: 0,
                totalCost: 0,
                totalRevenue: 0,
                goodPercentage: 0,
                poorPercentage: 0,
            },
        };
    }

    /**
     * @deprecated Legacy method - use fetchRoiAnalysis() instead
     * Analyze ROI data from campaign and product data
     */
    analyzeROI(campaignData, threshold = { good: 3.0, poor: 1.5 }) {
        console.warn(
            '⚠️ analyzeROI() is deprecated. Use fetchRoiAnalysis() instead.'
        );

        try {
            const allData = [
                ...(campaignData.campaigns || []),
                ...(campaignData.products || []),
            ];

            const goodRoiItems = allData.filter(
                (item) => item.roas && item.roas >= threshold.good
            );

            const poorRoiItems = allData.filter(
                (item) => item.roas && item.roas < threshold.poor
            );

            const totalCost = allData.reduce(
                (sum, item) => sum + (item.cost || item.costUSD || 0),
                0
            );
            const totalRevenue = allData.reduce(
                (sum, item) =>
                    sum + (item.grossRevenue || item.grossRevenueUSD || 0),
                0
            );

            return {
                goodRoi: {
                    count: goodRoiItems.length,
                    items: goodRoiItems.slice(0, 10), // Top 10
                    threshold: `>= ${threshold.good}`,
                },
                poorRoi: {
                    count: poorRoiItems.length,
                    items: poorRoiItems.slice(0, 10), // Top 10 worst
                    threshold: `< ${threshold.poor}`,
                },
                summary: {
                    total: allData.length,
                    totalCost: totalCost,
                    totalRevenue: totalRevenue,
                    goodPercentage:
                        allData.length > 0
                            ? (
                                  (goodRoiItems.length / allData.length) *
                                  100
                              ).toFixed(1)
                            : 0,
                    poorPercentage:
                        allData.length > 0
                            ? (
                                  (poorRoiItems.length / allData.length) *
                                  100
                              ).toFixed(1)
                            : 0,
                },
            };
        } catch (error) {
            console.error('❌ Error analyzing ROI:', error);
            return {
                goodRoi: {
                    count: 0,
                    items: [],
                    threshold: `>= ${threshold.good}`,
                },
                poorRoi: {
                    count: 0,
                    items: [],
                    threshold: `< ${threshold.poor}`,
                },
                summary: {
                    total: 0,
                    totalCost: 0,
                    totalRevenue: 0,
                    goodPercentage: 0,
                    poorPercentage: 0,
                },
            };
        }
    }

    /**
     * Analyze video status distribution
     */
    analyzeVideoStatus(videoData) {
        try {
            const creatives = videoData.creatives || [];
            const statusMap = new Map();

            // Define status mappings with improved icons and clearer text
            const statusInfo = {
                0: {
                    name: 'Đang chờ xử lý',
                    icon: '<i class="fas fa-hourglass-half"></i>',
                    color: 'warning',
                    key: 'IN_QUEUE',
                },
                1: {
                    name: 'Đang học tối ưu',
                    icon: '<i class="fas fa-graduation-cap"></i>',
                    color: 'info',
                    key: 'LEARNING',
                },
                2: {
                    name: 'Đang phát sóng',
                    icon: '<i class="fas fa-broadcast-tower"></i>',
                    color: 'success',
                    key: 'DELIVERING',
                },
                3: {
                    name: 'Không phát sóng',
                    icon: '<i class="fas fa-pause-circle"></i>',
                    color: 'danger',
                    key: 'NOT_DELIVERYIN',
                },
                4: {
                    name: 'Cần phê duyệt',
                    icon: '<i class="fas fa-user-check"></i>',
                    color: 'warning',
                    key: 'AUTHORIZATION_NEEDED',
                },
                5: {
                    name: 'Bị loại trừ',
                    icon: '<i class="fas fa-times-circle"></i>',
                    color: 'danger',
                    key: 'EXCLUDED',
                },
                6: {
                    name: 'Không khả dụng',
                    icon: '<i class="fas fa-exclamation-circle"></i>',
                    color: 'secondary',
                    key: 'UNAVAILABLE',
                },
                7: {
                    name: 'Bị từ chối',
                    icon: '<i class="fas fa-ban"></i>',
                    color: 'danger',
                    key: 'REJECTED',
                },
            };

            // Count by status
            creatives.forEach((creative) => {
                const status = creative.creativeDeliveryStatus;
                if (!statusMap.has(status)) {
                    statusMap.set(status, []);
                }
                statusMap.get(status).push(creative);
            });

            // Build status cards
            const statusCards = [];
            for (const [status, items] of statusMap) {
                const info = statusInfo[status] || {
                    name: `Status ${status}`,
                    icon: '<i class="fas fa-question-circle"></i>',
                    color: 'secondary',
                    key: `UNKNOWN_${status}`,
                };

                statusCards.push({
                    status: parseInt(status),
                    statusKey: info.key,
                    name: info.name,
                    icon: info.icon,
                    color: info.color,
                    count: items.length,
                    items: items,
                });
            }

            // Sort by count descending
            statusCards.sort((a, b) => b.count - a.count);

            // Get problematic videos (statuses 3, 5, 6, 7)
            const problematicStatuses = [3, 5, 6, 7]; // NOT_DELIVERYIN, EXCLUDED, UNAVAILABLE, REJECTED
            const problematicVideos = creatives.filter((creative) =>
                problematicStatuses.includes(creative.creativeDeliveryStatus)
            );

            return {
                statusCards,
                problematicVideos,
                summary: {
                    total: creatives.length,
                    problematicCount: problematicVideos.length,
                    problematicPercentage:
                        creatives.length > 0
                            ? (
                                  (problematicVideos.length /
                                      creatives.length) *
                                  100
                              ).toFixed(1)
                            : 0,
                },
            };
        } catch (error) {
            console.error('❌ Error analyzing video status:', error);
            return {
                statusCards: [],
                problematicVideos: [],
                summary: {
                    total: 0,
                    problematicCount: 0,
                    problematicPercentage: 0,
                },
            };
        }
    }

    /**
     * Fetch campaign data from API
     */
    async fetchCampaignData(filters) {
        const params = new URLSearchParams({
            fromDate: filters.fromDate || this.config.dateRange.from,
            toDate: filters.toDate || this.config.dateRange.to,
            currency: filters.currency || this.config.currency,
        });

        if (filters.shoppingAdsType) {
            params.append('shoppingAdsType', filters.shoppingAdsType);
        }

        // Add text search support
        if (filters.searchText) {
            params.append('searchText', filters.searchText);
        }

        // Add shop IDs filter
        if (filters.shopIds && filters.shopIds.length > 0) {
            filters.shopIds.forEach((id) => params.append('shopIds', id));
        }

        // Add campaign types filter
        if (filters.campaignTypes && filters.campaignTypes.length > 0) {
            filters.campaignTypes.forEach((type) =>
                params.append('campaignTypes', type)
            );
        }

        const response = await fetch(
            `${this.config.apiEndpoints.campaign}?${params}`,
            {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    /**
     * Fetch product data from API
     */
    async fetchProductData(filters) {
        const params = new URLSearchParams({
            fromDate: filters.fromDate || this.config.dateRange.from,
            toDate: filters.toDate || this.config.dateRange.to,
        });

        if (filters.creativeType) {
            params.append('creativeType', filters.creativeType);
        }

        // Add text search support
        if (filters.searchText) {
            params.append('searchText', filters.searchText);
        }

        // Add shop IDs filter
        if (filters.shopIds && filters.shopIds.length > 0) {
            filters.shopIds.forEach((id) => params.append('shopIds', id));
        }

        const response = await fetch(
            `${this.config.apiEndpoints.product}?${params}`,
            {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    /**
     * Fetch creative data from API
     */
    async fetchCreativeData(filters) {
        const params = new URLSearchParams({
            searchText: filters.searchText || '',
            fromDate: filters.fromDate || this.config.dateRange.from,
            toDate: filters.toDate || this.config.dateRange.to,
            skipCount: filters.skipCount || 0,
            maxResultCount: filters.maxResultCount || 20,
            sorting: filters.sorting || 'date desc',
        });

        // Add array parameters
        if (filters.campaignIds && filters.campaignIds.length > 0) {
            filters.campaignIds.forEach((id) =>
                params.append('campaignIds', id)
            );
        }
        if (filters.shopContentTypes && filters.shopContentTypes.length > 0) {
            filters.shopContentTypes.forEach((type) =>
                params.append('shopContentTypes', type)
            );
        }
        if (
            filters.creativeDeliveryStatuses &&
            filters.creativeDeliveryStatuses.length > 0
        ) {
            filters.creativeDeliveryStatuses.forEach((status) =>
                params.append('creativeDeliveryStatuses', status)
            );
        }

        const response = await fetch(
            `${this.config.apiEndpoints.creative}?${params}`,
            {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    /**
     * Get data from cache
     */
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;

        const now = Date.now();
        if (now - cached.timestamp > this.cacheTimeout) {
            this.cache.delete(key);
            return null;
        }

        return cached.data;
    }

    /**
     * Set data to cache
     */
    setCache(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
        });
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }
}
